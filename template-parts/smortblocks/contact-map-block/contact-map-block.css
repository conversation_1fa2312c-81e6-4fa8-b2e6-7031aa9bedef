.contact-map-block {
  padding: 80px 0;
  background-color: var(--accent-color);
  background-image: url("../../../img/smalandtimberbg.png");
  background-size: cover;
  background-position: center;
}

.contact-map-block__wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

/* Reverse layout - contact right, map left */
.contact-map-block--reverse .contact-map-block__contact {
  order: 2;
}

.contact-map-block--reverse .contact-map-block__map {
  order: 1;
}

.contact-map-block__title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 3rem;
  line-height: 1.2;
}

.contact-map-block__info {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.contact-map-block__item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  background-color: var(--secondary-color);
  padding: 1rem;
  border-radius: 10px;
  width: auto;
}

.contact-map-block__icon {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}

.contact-map-block__icon svg {
  width: 24px;
  height: 24px;
}

.contact-map-block__content h3 {
  font-family: var(--font-heading);
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.contact-map-block__content p {
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-black);
  margin: 0;
}

.contact-map-block__content a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-map-block__content a:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}

.contact-map-block__map {
  position: relative;
}

.contact-map-block__map-container {
  position: relative;
  width: 100%;
  height: 700px;
  border-radius: var(--border-radius-medium);
  overflow: hidden;
}

.contact-map-block__map-container iframe {
  width: 100% !important;
  height: 100% !important;
  border: 0;
}

@media (max-width: 768px) {
  .contact-map-block {
    padding: 60px 0;
  }

  .contact-map-block__wrapper {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .contact-map-block--reverse .contact-map-block__contact,
  .contact-map-block--reverse .contact-map-block__map {
    order: unset;
  }

  .contact-map-block__title {
    margin-bottom: 2rem;
    font-size: 2rem;
  }

  .contact-map-block__info {
    gap: 1.5rem;
  }

  .contact-map-block__map-container {
    height: 300px;
  }
}

@media (max-width: 480px) {
  .contact-map-block {
    padding: 40px 0;
  }

  .contact-map-block__wrapper {
    gap: 30px;
  }

  .contact-map-block__item {
    gap: 0.75rem;
  }

  .contact-map-block__icon {
    width: 40px;
    height: 40px;
  }

  .contact-map-block__icon svg {
    width: 20px;
    height: 20px;
  }
}
