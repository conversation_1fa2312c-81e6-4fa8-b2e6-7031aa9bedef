<?php
$title = get_field('contact_map_title') ?: 'Kontakta oss';
$address = get_field('contact_map_address');
$phone = get_field('contact_map_phone');
$email = get_field('contact_map_email');
$org_number = get_field('contact_map_org_number');
$vat_number = get_field('contact_map_vat_number');
$map_embed = get_field('contact_map_embed');
$layout = get_field('contact_map_layout') ?: 'contact-left';

$block_class = 'contact-map-block';
if ($layout === 'contact-right') {
    $block_class .= ' contact-map-block--reverse';
}
?>

<section class="<?php echo $block_class; ?>">
    <div class="container">
        <div class="contact-map-block__wrapper">
            <div class="contact-map-block__contact">
                <?php if ($title): ?>
                    <h2 class="contact-map-block__title"><?php echo esc_html($title); ?></h2>
                <?php endif; ?>

                <div class="contact-map-block__info">
                    <?php if ($address): ?>
                        <div class="contact-map-block__item">
                            <div class="contact-map-block__icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                                    <circle cx="12" cy="10" r="3"></circle>
                                </svg>
                            </div>
                            <div class="contact-map-block__content">
                                <h3>Adress</h3>
                                <p><?php echo nl2br(esc_html($address)); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($phone): ?>
                        <div class="contact-map-block__item">
                            <div class="contact-map-block__icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                                </svg>
                            </div>
                            <div class="contact-map-block__content">
                                <h3>Telefon</h3>
                                <p><a href="tel:<?php echo esc_attr(str_replace(' ', '', $phone)); ?>"><?php echo esc_html($phone); ?></a></p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($email): ?>
                        <div class="contact-map-block__item">
                            <div class="contact-map-block__icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                    <polyline points="22,6 12,13 2,6"></polyline>
                                </svg>
                            </div>
                            <div class="contact-map-block__content">
                                <h3>E-post</h3>
                                <p><a href="mailto:<?php echo esc_attr($email); ?>"><?php echo esc_html($email); ?></a></p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($org_number): ?>
                        <div class="contact-map-block__item">
                            <div class="contact-map-block__icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14,2 14,8 20,8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                    <polyline points="10,9 9,9 8,9"></polyline>
                                </svg>
                            </div>
                            <div class="contact-map-block__content">
                                <h3>Företaget</h3>
                                <p>Orgnr: <?php echo esc_html($org_number); ?></p>
                                <br>
                                <p>Vatnr: <?php echo esc_html($vat_number); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($map_embed): ?>
                <div class="contact-map-block__map">
                    <div class="contact-map-block__map-container">
                        <?php echo wp_kses($map_embed, array(
                            'iframe' => array(
                                'src' => array(),
                                'width' => array(),
                                'height' => array(),
                                'style' => array(),
                                'allowfullscreen' => array(),
                                'loading' => array(),
                                'referrerpolicy' => array(),
                            )
                        )); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>