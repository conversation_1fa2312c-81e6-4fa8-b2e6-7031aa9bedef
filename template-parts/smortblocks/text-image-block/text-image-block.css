.text-image-block {
  padding: 80px 0;
  background-color: var(--secondary-color);
  background-size: cover;
  background-position: center;
}

.text-image-block__wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.text-image-block--reverse .text-image-block__wrapper {
  grid-template-columns: 1fr 1fr;
}

.text-image-block--reverse .text-image-block__text {
  order: 2;
}

.text-image-block--reverse .text-image-block__image {
  order: 1;
}

.text-image-block__title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 2rem;
  line-height: 1.2;
}

.text-image-block__content {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-black);
}

.text-image-block__content p {
  margin-bottom: 1.5rem;
  font-size: 20px;
}

.text-image-block__content h3 {
  font-family: var(--font-heading);
  font-size: 1.8rem;
  font-weight: 600;
  margin: 2rem 0 1rem 0;
  color: var(--primary-color);
}

.text-image-block__content ul,
.text-image-block__content ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

.text-image-block__content li {
  margin-bottom: 0.5rem;
  font-size: 18px;
}

.text-image-block__image {
  position: relative;
}

.text-image-block__image img {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-medium);
  object-fit: cover;
  aspect-ratio: 4/3;
}

@media (max-width: 768px) {
  .text-image-block {
    padding: 60px 0;
  }

  .text-image-block__wrapper {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .text-image-block--reverse .text-image-block__text,
  .text-image-block--reverse .text-image-block__image {
    order: unset;
  }

  .text-image-block__title {
    margin-bottom: 1.5rem;
    font-size: 2rem;
  }

  .text-image-block__content p,
  .text-image-block__content li {
    font-size: 16px;
  }

  .text-image-block__image img {
    aspect-ratio: 16/10;
  }

  .text-image-block--reverse .text-image-block__wrapper {
    grid-template-columns: 2fr;
  }
}

@media (max-width: 480px) {
  .text-image-block {
    padding: 40px 0;
  }

  .text-image-block__wrapper {
    gap: 30px;
  }
}
