<?php
$title = get_field('text_image_title');
$content = get_field('text_image_content');
$image = get_field('text_image_image');
$layout = get_field('text_image_layout') ?: 'text-left';

$block_class = 'text-image-block';
if ($layout === 'text-right') {
    $block_class .= ' text-image-block--reverse';
}
?>

<section class="<?php echo $block_class; ?>">
    <div class="container">
        <div class="text-image-block__wrapper">
            <div class="text-image-block__text">
                <?php if ($title): ?>
                    <h2 class="text-image-block__title"><?php echo esc_html($title); ?></h2>
                <?php endif; ?>

                <?php if ($content): ?>
                    <div class="text-image-block__content">
                        <?php echo wp_kses_post($content); ?>
                    </div>
                <?php endif; ?>
            </div>

            <?php if ($image): ?>
                <div class="text-image-block__image">
                    <img src="<?php echo esc_url($image['url']); ?>"
                        alt="<?php echo esc_attr($image['alt']); ?>">
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>