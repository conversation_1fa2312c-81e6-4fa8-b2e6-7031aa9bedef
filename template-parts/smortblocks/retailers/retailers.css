.retailers-block {
  padding: 80px 0;
  background-color: var(--secondary-color);
}

.retailers-header {
  text-align: center;
  margin-bottom: 40px;
}

.retailers-subtitle-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.retailers-subtitle {
  color: var(--primary-color);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.5px;
}

.retailers-title {
  margin-bottom: 0;
  color: var(--primary-color);
  font-size: var(--font-size-h2);
}

.retailers-wrapper {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 0;
  align-items: start;
  margin: 0 auto;
  background: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.retailers-list {
  background: var(--white);
  padding: 40px 30px;
  max-height: 800px;
  overflow-y: auto;
}

.retailers-list-title {
  color: var(--primary-colorr);
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-bold);
  margin-bottom: 30px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.retailers-list::-webkit-scrollbar {
  width: 4px;
}

.retailers-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

.retailers-list::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 2px;
}

.retailer-card {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 15px 0;
  box-shadow: none;
  transition: all 0.2s ease;
  cursor: pointer;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.retailer-card:last-child {
  border-bottom: none;
}

.retailer-card:hover,
.retailer-card.highlighted {
  background-color: rgba(30, 77, 45, 0.05);
  transform: none;
  box-shadow: none;
  padding-left: 10px;
}

.retailer-icon {
  display: none;
}

.retailer-name {
  color: var(--text-black);
  margin-bottom: 8px;
  font-size: var(--font-size-p);
  font-weight: var(--font-weight-semibold);
}

.retailer-address {
  display: block;
  margin-bottom: 4px;
}

.retailer-address p {
  margin: 0;
  color: #666;
  font-size: var(--font-size-small);
  line-height: 1.4;
}

.retailer-phone {
  display: block;
  margin-bottom: 0;
}

.retailer-phone a {
  margin: 0;
  color: #666;
  font-size: var(--font-size-small);
  text-decoration: none;
  transition: color 0.3s ease;
}

.retailer-phone a:hover {
  color: var(--primary-color);
}

.retailers-map {
  position: relative;
  background: #f5f5f5;
}

#retailers-google-map {
  border-radius: 0;
  box-shadow: none;
  width: 100%;
  height: 600px;
}

/* Map controls styling */
.gm-style .gm-style-iw-c {
  border-radius: var(--border-radius-medium);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.gm-style .gm-style-iw-t::after {
  background: var(--white);
}

@media (max-width: 1024px) {
  .retailers-wrapper {
    grid-template-columns: 1fr;
    max-width: 100%;
  }

  .retailers-list {
    max-height: 400px;
    padding: 30px 20px;
  }

  #retailers-google-map {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .retailers-wrapper {
    border-radius: var(--border-radius-medium);
  }

  .retailers-list {
    padding: 20px 15px;
  }

  #retailers-google-map {
    height: 400px;
  }
}
