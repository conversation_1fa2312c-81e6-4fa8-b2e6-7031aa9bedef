<?php

/**
 * Retailers Block Template
 */

if (!function_exists('get_field')) {
    return;
}

// Sätt default för $block om den inte finns (t.ex. vid get_template_part)
if (!isset($block)) {
    $block = [
        'id' => uniqid('retailers-'),
        'anchor' => '',
        'className' => '',
        'align' => ''
    ];
}
// Create class attribute
$classes = 'retailers-block';
if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}

// Get block fields
$title = get_field('retailers_title') ?: 'Våra återförsäljare';
$subtitle = get_field('retailers_subtitle');

// Get retailers from options page
$retailers = get_field('retailers_list', 'option');
$google_maps_api_key = get_field('google_maps_api_key', 'option');

?>

<section class="<?php echo esc_attr($classes); ?>">
    <div class="container">
        <div class="retailers-header">
            <?php if ($subtitle): ?>
                <div class="retailers-subtitle-container">
                    <span class="retailers-subtitle"><?php echo esc_html($subtitle); ?></span>
                </div>
            <?php endif; ?>

            <?php if ($title): ?>
                <h2 class="retailers-title"><?php echo esc_html($title); ?></h2>
            <?php endif; ?>
        </div>

        <?php if ($retailers): ?>
            <div class="retailers-wrapper">
                <!-- Vänster sida - Lista över återförsäljare -->
                <div class="retailers-list">
                    <h3 class="retailers-list-title">Återförsäljare</h3>
                    <?php foreach ($retailers as $index => $retailer): ?>
                        <div class="retailer-card" data-retailer-index="<?php echo $index; ?>">
                            <h4 class="retailer-name"><?php echo esc_html($retailer['name']); ?></h4>

                            <?php if (!empty($retailer['address'])): ?>
                                <div class="retailer-address">
                                    <p><?php echo esc_html($retailer['address']); ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($retailer['phone'])): ?>
                                <div class="retailer-phone">
                                    <a href="tel:<?php echo esc_attr($retailer['phone']); ?>">
                                        <?php echo esc_html($retailer['phone']); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Höger sida - Karta -->
                <div class="retailers-map">
                    <div id="retailers-google-map" style="height: 800px;"></div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php if ($retailers && $google_maps_api_key): ?>
    <script>
        let map;
        let markers = [];
        let infoWindows = [];

        function initRetailersMap() {
            // Centrera kartan på Sverige
            const center = {
                lat: 62.0,
                lng: 15.0
            };

            // Custom map styling med temats färger
            const mapStyles = [{
                    "featureType": "all",
                    "elementType": "geometry.fill",
                    "stylers": [{
                        "color": "#f3f8e4"
                    }]
                },
                {
                    "featureType": "water",
                    "elementType": "geometry",
                    "stylers": [{
                        "color": "#9db4c7"
                    }]
                },
                {
                    "featureType": "landscape",
                    "elementType": "geometry",
                    "stylers": [{
                        "color": "#f9fbf3"
                    }]
                },
                {
                    "featureType": "road",
                    "elementType": "geometry",
                    "stylers": [{
                        "color": "#ffffff"
                    }]
                },
                {
                    "featureType": "administrative",
                    "elementType": "labels.text.fill",
                    "stylers": [{
                        "color": "#1e4d2d"
                    }]
                },
                {
                    "featureType": "poi",
                    "elementType": "labels",
                    "stylers": [{
                        "visibility": "off"
                    }]
                }
            ];

            map = new google.maps.Map(document.getElementById('retailers-google-map'), {
                zoom: 6,
                center: {
                    lat: 62.0,
                    lng: 15.0
                },
                styles: mapStyles,
                mapTypeControl: true,
                mapTypeControlOptions: {
                    style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
                    position: google.maps.ControlPosition.TOP_RIGHT,
                    mapTypeIds: ['roadmap', 'satellite']
                },
                zoomControl: true,
                zoomControlOptions: {
                    position: google.maps.ControlPosition.RIGHT_BOTTOM
                },
                streetViewControl: false,
                fullscreenControl: true,
                fullscreenControlOptions: {
                    position: google.maps.ControlPosition.RIGHT_TOP
                }
            });

            const retailers = <?php echo json_encode($retailers); ?>;

            retailers.forEach((retailer, index) => {
                if (retailer.latitude && retailer.longitude) {
                    const position = {
                        lat: parseFloat(retailer.latitude),
                        lng: parseFloat(retailer.longitude)
                    };

                    const marker = new google.maps.Marker({
                        position: position,
                        map: map,
                        title: retailer.name,
                        icon: {
                            url: 'data:image/svg+xml;utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 384 512"><path fill="%231e4d2d" d="M192 0C86 0 0 86 0 192c0 77.7 99.5 221.6 146.6 284.7 9.6 12.5 27.2 12.5 36.8 0C284.5 413.6 384 269.7 384 192 384 86 298 0 192 0zm0 272a80 80 0 1 1 0-160 80 80 0 1 1 0 160z"/></svg>',
                            scaledSize: new google.maps.Size(40, 40),
                            anchor: new google.maps.Point(20, 40)
                        }
                    });

                    // Custom info window styling
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 15px; max-width: 280px; font-family: 'beaufort-pro', serif;">
                                <h4 style="margin: 0 0 12px 0; color: #1e4d2d; font-size: 16px; font-weight: 600;">${retailer.name}</h4>
                                ${retailer.address ? `<p style="margin: 8px 0; font-size: 14px; color: #666; line-height: 1.4;">${retailer.address}</p>` : ''}
                                ${retailer.phone ? `<p style="margin: 8px 0 0 0; font-size: 14px;"><a href="tel:${retailer.phone}" style="color: #1e4d2d; text-decoration: none; font-weight: 500;">${retailer.phone}</a></p>` : ''}
                            </div>
                        `
                    });

                    marker.addListener('click', () => {
                        // Stäng alla andra infoWindows
                        infoWindows.forEach(iw => iw.close());
                        infoWindow.open(map, marker);

                        // Highlighta motsvarande kort
                        document.querySelectorAll('.retailer-card').forEach(card => {
                            card.classList.remove('highlighted');
                        });
                        const card = document.querySelector(`[data-retailer-index="${index}"]`);
                        if (card) {
                            card.classList.add('highlighted');
                            card.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                        }
                    });

                    markers.push(marker);
                    infoWindows.push(infoWindow);
                }
            });

            // Lägg till click-lyssnare på korten
            document.querySelectorAll('.retailer-card').forEach((card, index) => {
                card.addEventListener('click', () => {
                    if (markers[index]) {
                        // Centrera kartan på markören
                        map.setCenter(markers[index].getPosition());
                        map.setZoom(12);

                        // Öppna info window
                        infoWindows.forEach(iw => iw.close());
                        infoWindows[index].open(map, markers[index]);

                        // Highlighta kortet
                        document.querySelectorAll('.retailer-card').forEach(c => c.classList.remove('highlighted'));
                        card.classList.add('highlighted');
                    }
                });
            });
        }

        // Ladda Google Maps API
        if (typeof google === 'undefined') {
            const script = document.createElement('script');
            script.src = `https://maps.googleapis.com/maps/api/js?key=<?php echo esc_js($google_maps_api_key); ?>&callback=initRetailersMap`;
            script.async = true;
            script.defer = true;
            document.head.appendChild(script);
        } else {
            initRetailersMap();
        }
    </script>
<?php endif; ?>