<?php

/**
 * Cta Section Block Template
 * 
 * @param array $block The block settings and attributes.
 * @param string $content The block inner HTML (empty).
 * @param bool $is_preview True during AJAX preview.
 * @param int|string $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'cta-section-' . $block['id'];
if (!empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$prefix = 'cta-section';

// Load values and assign defaults.
$title = !empty(get_field('title')) ? get_field('title') : null;
$repeater = !empty(get_field('repeater')) ? get_field('repeater') : null;
$default_image = !empty(get_field('default_image')) ? get_field('default_image') : null;
$preamble = !empty(get_field('preamble')) ? get_field('preamble') : null;
$button_link = !empty(get_field('button_link')) ? get_post(get_field('button_link')) : null;



?>
<div class=" <?= $prefix ?>">

    <section id="<?php echo esc_attr($id); ?>" class="<?= $prefix ?>-wrapper container">

        <div class="<?= $prefix ?>-top">


            <?php if (!empty($button_link)) : ?>
                <a href="<?php echo get_permalink($button_link); ?>" class="<?= $prefix ?>-top-link">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#1e4d2d" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-corner-down-right">
                        <polyline points="15 10 20 15 15 20"></polyline>
                        <path d="M4 4v7a4 4 0 0 0 4 4h12"></path>
                    </svg>
                    <span>
                        <?= $button_link->post_title; ?></a>
                </span>
            <?php endif; ?>


            <?php if (!empty($title)) : ?>
                <h4 class="<?= $prefix ?>-top-title">
                    <?php echo esc_html($title); ?>
                </h4>
            <?php endif; ?>

        </div>

        <div class="<?= $prefix ?>-bottom">

            <div class="<?= $prefix ?>-bottom-left">
                <?php foreach ($repeater as $index => $row) : ?>
                    <?php if (!empty($row['text'])) : ?>
                        <?php if ($row['is_link'] && !empty($row['page'])) : ?>
                            <a class="<?= $prefix ?>-row" href="<?php echo get_permalink($row['page']); ?>" data-hover-index="<?php echo $index; ?>">
                            <?php else : ?>
                                <div class="<?= $prefix ?>-row" data-hover-index="<?php echo $index; ?>">
                                <?php endif; ?>
                                <div class="<?= $prefix ?>-row-left">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#1e4d2d" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-corner-down-right">
                                        <polyline points="15 10 20 15 15 20"></polyline>
                                        <path d="M4 4v7a4 4 0 0 0 4 4h12"></path>
                                    </svg>
                                    <h5 class="<?= $prefix ?>-row-title"><?= $row['text']; ?></h5>
                                </div>

                                <?php if ($row['image']) : ?>
                                    <img src="<?php echo esc_url($row['image']['url']); ?>"
                                        alt="<?php echo esc_attr($row['image']['alt'] ?? $row['text']); ?>"
                                        class="cta-section-mobile-img">
                                <?php endif; ?>


                                <?php if ($row['is_link']) : ?>
                            </a>
                        <?php else : ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
<?php endforeach; ?>

<?php if (!empty($preamble) || !empty($button_link)) : ?>
    <div class="<?= $prefix ?>-bottom-left-bottom ">
        <?php if (!empty($preamble)) : ?>
            <p class="<?= $prefix ?>-preamble"><?php echo esc_html($preamble); ?></p>
        <?php endif; ?>
        <?php if (!empty($button_link)) : ?>
            <?php _sc('button', [
                'href' => get_permalink($button_link),
                'text' => $button_link->post_title,
                'type' => 'accent',
            ]); ?>
        <?php endif; ?>
    </div>
<?php endif; ?>

        </div>

        <div class="<?= $prefix ?>-bottom-right">
            <?php if (!empty($default_image)) : ?>
                <img src="<?php echo esc_url($default_image['url']); ?>" alt="<?php echo esc_attr($default_image['alt']); ?>" class="<?= $prefix ?>-image hover-img default-img active">
            <?php endif; ?>

            <?php if (!empty($repeater)) : ?>
                <?php foreach ($repeater as $index => $row) : ?>
                    <?php if (!empty($row['image'])) : ?>
                        <img src="<?php echo esc_url($row['image']['url']); ?>"
                            alt="<?php echo esc_attr($row['image']['alt'] ?? $row['text']); ?>"
                            class="hover-img <?= $prefix ?>-image"
                            data-img-index="<?php echo $index; ?>">
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>

        </div>

</div>
</section>
</div>