document.addEventListener("DOMContentLoaded", function () {
  const links = document.querySelectorAll(".cta-section-row");
  const images = document.querySelectorAll(".hover-img");
  const defaultImg = document.querySelector(".default-img");
console.log('🟡 ~ images',images);
  const cursor = document.createElement("div");
  cursor.classList.add("cta-custom-cursor");
  document.body.appendChild(cursor);

  links.forEach((link) => {
    const index = link.getAttribute("data-hover-index");

    link.addEventListener("mouseenter", () => {
      cursor.classList.add("active");
      images.forEach((img) => img.classList.remove("active"));
      const hoverImg = document.querySelector(
        `.hover-img[data-img-index="${index}"]`
      );
      if (hoverImg) {
        hoverImg.classList.add("active");
      }
    });

    link.addEventListener("mouseleave", () => {
      cursor.classList.remove("active");
      images.forEach((img) => img.classList.remove("active"));
      if (defaultImg) {
        defaultImg.classList.add("active");
      }
    });

    // Uppdatera cursor position
    link.addEventListener("mousemove", (e) => {
      cursor.style.left = e.clientX + "px";
      cursor.style.top = e.clientY + "px";
    });
  });
});