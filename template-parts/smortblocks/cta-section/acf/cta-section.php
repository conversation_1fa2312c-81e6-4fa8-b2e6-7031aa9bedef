<?php

/**
 * ACF-fält för Cta Section block
 */

if (function_exists('acf_add_local_field_group')) {
    acf_add_local_field_group(array(
        'key' => 'group_cta_section_fields',
        'title' => 'Cta Hover - Block',
        'fields' => array(
            array(
                'key' => 'field_cta_section_title',
                'label' => 'Rubrik',
                'name' => 'title',
                'type' => 'text',
                'instructions' => 'Enter the title for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'Enter text...',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'field_cta_section_repeater',
                'label' => 'Repeater',
                'name' => 'repeater',
                'type' => 'repeater',
                'instructions' => 'Add content rows to your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'layout' => 'column',
                'sub_fields' => array(
                    array(
                        'key' => 'field_cta_section_text',
                        'label' => 'Text',
                        'name' => 'text',
                        'type' => 'text',
                        'instructions' => 'Enter text for this row',
                        'required' => 0,
                    ),
                    array(
                        'key' => 'field_cta_section_page',
                        'label' => 'Länk',
                        'name' => 'page',
                        'type' => 'post_object',
                        'post_type' => array('page'),
                        'return_format' => 'id',
                        'ui' => 1,
                        'instructions' => 'Select a page to link to',
                        'required' => 0,
                    ),
                    array(
                        'key' => 'field_cta_section_row_image',
                        'label' => 'Bild',
                        'name' => 'image',
                        'type' => 'image',
                        'instructions' => 'Upload an image for this row',
                        'required' => 0,
                        'return_format' => 'array',
                        'preview_size' => 'medium',
                        'library' => 'all',
                    ),
                    array(
                        'key' => 'field_cta_section_is_link',
                        'label' => 'Är länkbar',
                        'name' => 'is_link',
                        'type' => 'true_false',
                        'instructions' => 'Är denna rad länkbar?',
                        'required' => 0,
                    )
                ),
            ),
            array(
                'key' => 'field_cta_section_image',
                'label' => 'Default Image',
                'name' => 'default_image',
                'type' => 'image',
                'instructions' => 'Enter the image for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'min_width' => '',
                'min_height' => '',
                'min_size' => '',
                'max_width' => '',
                'max_height' => '',
                'max_size' => '',
                'mime_types' => '',
            ),
            array(
                'key' => 'field_cta_section_preamble',
                'label' => 'Preamble',
                'name' => 'preamble',
                'type' => 'text',
                'instructions' => 'Enter the preamble for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'Enter text...',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'field_cta_section_button_link',
                'label' => 'Button Link',
                'name' => 'button_link',
                'type' => 'post_object',
                'post_type' => array('page'),
                'return_format' => 'id',
                'ui' => 1,
                'instructions' => 'Select a page to link to',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
            )
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/cta-section',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => 'Fields for the Cta Section block',
    ));
}
