/**
 * Cta Section Block Styles
 */

.cta-section {
  /* Main block container */
  background-color: var(--primary-color);
  padding-block: 6rem;
}

.cta-section-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4xl);

  @media (width <= 768px) {
    gap: var(--spacing-2xl);
  }
}
.cta-section-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;

  .cta-section-top-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    svg,
    path {
      stroke: var(--secondary-color);
      height: 20px;
      width: 20px;
    }
    span {
      font-family: var(--font-body);
      color: var(--secondary-color);
    }
  }

  .cta-section-top-title {
    color: var(--secondary-color);
    margin: 0;
  }
}
.cta-section-bottom {
  display: flex;
  justify-content: space-between;
  gap: 2rem;

  .cta-section-bottom-left {
    width: 58%;

    .cta-section-row {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: var(--spacing-xl) 0;
      border-bottom: 1px solid var(--secondary-color);
      color: var(--secondary-color);
      .cta-section-row-left {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        @media (width <= 768px) {
          flex-direction: column;
          align-items: flex-start;
          gap: var(--spacing-sm);
        }
      }
      &:hover {
        color: var(--secondary-color);
      }
      svg,
      path,
      polyline {
        color: var(--secondary-color);
        stroke: var(--secondary-color);
      }
      .cta-section-row-title {
        margin: 0;
        /* margin-top: 1rem; */
        font-size:var(--font-size-h3);
      }
    }

    .cta-section-bottom-left-bottom {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      margin-top: 2rem;

      .cta-section-preamble {
        font-family: var(--font-body);
        color: var(--secondary-color);
        max-width: 70%;

        @media (max-width: 768px) {
          max-width: 100%;
        }
      }
    }
  }
  .cta-section-bottom-right {
    width: 42%;
    overflow: hidden;
    position: relative;
    img {
      width: 100%;
      height: auto;
      object-fit: cover;
      border-radius: var(--border-radius-medium);
    }
  }

  .cta-section-bottom-right img.hover-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.4s ease-in-out;
    z-index: 0;
  }

  .cta-section-bottom-right img.hover-img.active {
    opacity: 1;
    z-index: 1;
  }

  .cta-section-mobile-img {
    display: none;
    aspect-ratio: 1/1;
    width: 100%;
    height: auto;
    object-fit: cover;
    max-width: 100px;
    border-radius: var(--border-radius-medium);
  }
}

.cta-custom-cursor {
  position: fixed;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #fff;
  pointer-events: none;
  mix-blend-mode: difference;
  transform: translate(-50%, -50%) scale(0);
  z-index: 9999;
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.cta-custom-cursor.active {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

@media screen and (max-width: 768px) {
  .cta-custom-cursor {
    display: none;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .cta-section-row-title {
    /* font-size: 3rem !important; */
  }

  .cta-section-top {
    align-items: flex-start;

    .cta-section-top-title {
      /* font-size: 2rem; */
    }
  }
  .cta-section-bottom {
    .cta-section-row {
      justify-content: space-between;
    }
    img.cta-section-mobile-img {
      display: block;
    }
    .cta-section-bottom-left {
      width: 100%;

      .cta-section-row {
        h5 {
          /* font-size: 2rem; */
        }
      }
    }
    .cta-section-bottom-right {
      display: none;
    }
  }
}
