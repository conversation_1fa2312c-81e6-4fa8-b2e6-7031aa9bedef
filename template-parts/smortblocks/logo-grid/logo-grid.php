<?php
$title = get_field('logo_grid_title');
$subtitle = get_field('logo_grid_subtitle');
$logos = get_field('logo_grid_logos');
$background_color = get_field('logo_grid_background') ?: 'accent';

$block_class = 'logo-grid';
if ($background_color === 'secondary') {
    $block_class .= ' logo-grid--secondary';
} elseif ($background_color === 'primary') {
    $block_class .= ' logo-grid--primary';
}
?>

<section class="<?php echo $block_class; ?>">
    <div class="container">
        <div class="logo-grid__wrapper">
            <?php if ($subtitle): ?>
                <p class="logo-grid__subtitle"><?php echo esc_html($subtitle); ?></p>
            <?php endif; ?>

            <?php if ($title): ?>
                <h2 class="logo-grid__title"><?php echo esc_html($title); ?></h2>
            <?php endif; ?>

            <?php if ($logos): ?>
                <div class="logo-grid__grid">
                    <?php foreach ($logos as $logo): ?>
                        <div class="logo-grid__item">
                            <img src="<?php echo esc_url($logo['logo']['url']); ?>"
                                alt="<?php echo esc_attr($logo['logo']['alt'] ?: $logo['company_name']); ?>"
                                class="logo-grid__image">
                            <?php if ($logo['company_name']): ?>
                                <span class="logo-grid__name"><?php echo esc_html($logo['company_name']); ?></span>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>