.logo-grid {
  padding-bottom: 80px;
  background-color: var(--accent-color);

  background-size: cover;
  background-position: center;
}

.logo-grid--secondary {
  background-color: var(--secondary-color);
}

.logo-grid--primary {
  background-color: var(--primary-color);
}

.logo-grid--primary .logo-grid__title,
.logo-grid--primary .logo-grid__subtitle {
  color: var(--text-white);
}

.logo-grid__wrapper {
  max-width: 700px;
  margin: 0 auto;
  text-align: center;
}

.logo-grid__subtitle {
  color: var(--primary-color);
  font-size: 18px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 1rem;
}

.logo-grid__title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 3rem;
  line-height: 1.2;
}

.logo-grid__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  align-items: center;
}

.logo-grid__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 30px 20px;
  border-radius: var(--border-radius-medium);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.logo-grid__image {
  max-width: 100px;
  max-height: auto;
  width: auto;
  height: auto;
  object-fit: contain;
  transition: filter 0.3s ease;
}

.logo-grid__item:hover .logo-grid__image {
  filter: grayscale(0%);
}

.logo-grid__name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-black);
  text-align: center;
}

@media (max-width: 768px) {
  .logo-grid {
    padding: 60px 0;
  }

  .logo-grid__grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 30px;
  }

  .logo-grid__item {
    padding: 20px 15px;
  }

  .logo-grid__image {
    max-width: 120px;
    max-height: auto;
  }
}

@media (max-width: 480px) {
  .logo-grid {
    padding: 40px 0;
  }

  .logo-grid__grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}
