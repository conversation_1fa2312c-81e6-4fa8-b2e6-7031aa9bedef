<?php

/**
 * ACF fields for Trävaror page template
 */

if (function_exists('acf_add_local_field_group')) {
    acf_add_local_field_group(array(
        'key' => 'group_services_page_fields',
        'title' => 'Trävaror Page Fields',
        'fields' => array(
            array(
                'key' => 'field_services_hero_title',
                'label' => 'Hero Titel',
                'name' => 'services_hero_title',
                'type' => 'text',
                'instructions' => 'Anpassad titel för hero-sektionen. Lämna tom för att använda sidans titel.',
                'required' => 0,
                'placeholder' => 'Ange hero titel...',
                'default_value' => '',
            ),
            array(
                'key' => 'field_services_hero_subtitle',
                'label' => 'Hero Undertitel',
                'name' => 'services_hero_subtitle',
                'type' => 'text',
                'instructions' => 'Undertitel som visas ovanför huvudtiteln.',
                'required' => 0,
                'placeholder' => 'Våra produkter',
                'default_value' => 'Våra produkter',
            ),
            array(
                'key' => 'field_services_hero_description',
                'label' => 'Hero Beskrivning',
                'name' => 'services_hero_description',
                'type' => 'textarea',
                'instructions' => 'Beskrivning som visas under titeln. Lämna tom för att använda sidans innehåll.',
                'required' => 0,
                'rows' => 3,
                'placeholder' => 'Ange beskrivning...',
            ),
            array(
                'key' => 'field_services_hero_background',
                'label' => 'Hero Bakgrundsbild',
                'name' => 'services_hero_background',
                'type' => 'image',
                'instructions' => 'Bakgrundsbild för hero-sektionen.',
                'required' => 0,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
            ),
            array(
                'key' => 'field_services_hero_overlay_opacity',
                'label' => 'Overlay Opacitet (%)',
                'name' => 'services_hero_overlay_opacity',
                'type' => 'number',
                'instructions' => 'Justera genomskinligheten på det mörka lagret över bilden.',
                'required' => 0,
                'default_value' => 70,
                'min' => 0,
                'max' => 100,
                'step' => 5,
            ),
            array(
                'key' => 'field_services_intro_image',
                'label' => 'Intro Bild',
                'name' => 'services_intro_image',
                'type' => 'image',
                'instructions' => 'Bild som visas bredvid intro-texten.',
                'required' => 0,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
            ),

            array(
                'key' => 'field_services_category_filter',
                'label' => 'Filtrera på kategori',
                'name' => 'services_category_filter',
                'type' => 'taxonomy',
                'instructions' => 'Välj vilken kategori av tjänster som ska visas på sidan.',
                'required' => 0,
                'taxonomy' => 'services_category',
                'field_type' => 'select',
                'allow_null' => 1,
                'add_term' => 0,
                'save_terms' => 0,
                'load_terms' => 0,
                'return_format' => 'id',
                'multiple' => 0,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'page_template',
                    'operator' => '==',
                    'value' => 'page-services.php',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'active' => true,
        'description' => 'Fields for Trävaror page template',
    ));
}
