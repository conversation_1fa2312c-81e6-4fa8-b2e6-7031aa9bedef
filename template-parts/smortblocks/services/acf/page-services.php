<?php

/**
 * ACF fields for Trävaror page template
 */

if (function_exists('acf_add_local_field_group')) {
    acf_add_local_field_group(array(
        'key' => 'group_services_page_fields',
        'title' => 'Trävaror Page Fields',
        'fields' => array(
            array(
                'key' => 'field_services_hero_title',
                'label' => 'Hero Titel',
                'name' => 'services_hero_title',
                'type' => 'text',
                'instructions' => 'Anpassad titel för hero-sektionen. Lämna tom för att använda sidans titel.',
                'required' => 0,
                'placeholder' => 'Ange hero titel...',
                'default_value' => '',
            ),
            array(
                'key' => 'field_services_hero_subtitle',
                'label' => 'Hero Undertitel',
                'name' => 'services_hero_subtitle',
                'type' => 'text',
                'instructions' => 'Undertitel som visas ovanför huvudtiteln.',
                'required' => 0,
                'placeholder' => 'Våra produkter',
                'default_value' => 'Våra produkter',
            ),
            array(
                'key' => 'field_services_hero_description',
                'label' => 'Hero Beskrivning',
                'name' => 'services_hero_description',
                'type' => 'textarea',
                'instructions' => 'Beskrivning som visas under titeln. Lämna tom för att använda sidans innehåll.',
                'required' => 0,
                'rows' => 3,
                'placeholder' => 'Ange beskrivning...',
            ),
            array(
                'key' => 'field_services_hero_background',
                'label' => 'Hero Bakgrundsbild',
                'name' => 'services_hero_background',
                'type' => 'image',
                'instructions' => 'Bakgrundsbild för hero-sektionen.',
                'required' => 0,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
            ),

            array(
                'key' => 'field_services_intro_image',
                'label' => 'Intro Bild',
                'name' => 'services_intro_image',
                'type' => 'image',
                'instructions' => 'Bild som visas bredvid intro-texten.',
                'required' => 0,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
            ),

            //service intro text
            array(
                'key' => 'field_services_intro_title',
                'label' => 'Intro Titel',
                'name' => 'services_intro_title',
                'type' => 'text',
                'instructions' => 'Titel som visas bredvid intro-bilden.',
                'required' => 0,
                'placeholder' => 'Våra produkter',
                'default_value' => 'Våra produkter',
            ),
            array(
                'key' => 'field_services_intro_text',
                'label' => 'Intro Text',
                'name' => 'services_intro_text',
                'type' => 'wysiwyg',
                'instructions' => 'Text som visas bredvid intro-bilden.',
                'required' => 0,
                'toolbar' => 'basic',
                'media_upload' => 0,
            ),

            array(
                'key' => 'field_services_category_filter',
                'label' => 'Filtrera på kategori',
                'name' => 'services_category_filter',
                'type' => 'taxonomy',
                'instructions' => 'Välj vilken kategori av tjänster som ska visas på sidan.',
                'required' => 0,
                'taxonomy' => 'services_category',
                'field_type' => 'multi_select',
                'allow_null' => 1,
                'add_term' => 0,
                'save_terms' => 0,
                'load_terms' => 0,
                'return_format' => 'id',
                'multiple' => 1,
            ),

            array(
                'key' => 'field_hotspot_image',
                'label' => 'Hotspot-bild',
                'name' => 'hotspot_image',
                'type' => 'image',
                'instructions' => 'Ladda upp bilden för hotspots.',
                'required' => 1,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
            ),

            array(
                'key' => 'field_hotspot_title',
                'label' => 'Hotspot huvudrubrik',
                'name' => 'hotspot_title',
                'type' => 'text',
                'instructions' => 'Rubrik för hotspot-sektionen.',
                'required' => 0,
                'default_value' => '',
            ),
            array(
                'key' => 'field_hotspot_description',
                'label' => 'Hotspot beskrivning',
                'name' => 'hotspot_description',
                'type' => 'wysiwyg',
                'instructions' => 'Beskrivning för hotspot-sektionen.',
                'required' => 0,
                'default_value' => '',
            ),

            array(
                'key' => 'field_hotspots',
                'label' => 'Hotspots',
                'name' => 'hotspots',
                'type' => 'repeater',
                'instructions' => 'Lägg till hotspots på bilden.',
                'required' => 0,
                'min' => 0,
                'max' => 0,
                'layout' => 'row',
                'button_label' => 'Lägg till hotspot',
                'sub_fields' => array(
                    array(
                        'key' => 'field_hotspot_title',
                        'label' => 'Titel',
                        'name' => 'title',
                        'type' => 'text',
                        'required' => 1,
                    ),
                    array(
                        'key' => 'field_hotspot_x',
                        'label' => 'Vänster (%)',
                        'name' => 'x',
                        'type' => 'number',
                        'instructions' => 'Vänsterposition i procent (0-100)',
                        'required' => 1,
                        'min' => 0,
                        'max' => 100,
                    ),
                    array(
                        'key' => 'field_hotspot_y',
                        'label' => 'Topp (%)',
                        'name' => 'y',
                        'type' => 'number',
                        'instructions' => 'Topposition i procent (0-100)',
                        'required' => 1,
                        'min' => 0,
                        'max' => 100,
                    ),
                ),
            ),

            //FAQ
            array(
                'key' => 'field_services_faq',
                'label' => 'Vanliga frågor',
                'name' => 'services_faq',
                'type' => 'repeater',
                'instructions' => 'Lägg till vanliga frågor och svar.',
                'required' => 0,
                'min' => 0,
                'max' => 0,
                'layout' => 'row',
                'button_label' => 'Lägg till fråga',
                'sub_fields' => array(
                    array(
                        'key' => 'field_services_faq_question',
                        'label' => 'Fråga',
                        'name' => 'question',
                        'type' => 'text',
                        'required' => 1,
                    ),
                    array(
                        'key' => 'field_services_faq_answer',
                        'label' => 'Svar',
                        'name' => 'answer',
                        'type' => 'textarea',
                        'required' => 1,
                    ),
                ),
            ),
            array(
                'key' => 'field_services_faq_simple',
                'label' => 'Vanliga frågor (Enkel)',
                'name' => 'services_faq_simple',
                'type' => 'textarea',
                'instructions' => 'Skriv frågor och svar. Format: Fråga? | Svar här. En per rad.',
                'required' => 0,
                'rows' => 10,
                'placeholder' => 'Vad kostar leverans? | Leverans kostar 500kr\nHur lång leveranstid? | 2-3 veckor',
            ),
            array(
                'key' => 'field_services_faq_wysiwyg',
                'label' => 'Vanliga frågor (HTML)',
                'name' => 'services_faq_wysiwyg',
                'type' => 'wysiwyg',
                'instructions' => 'Skriv frågor och svar med HTML-struktur.',
                'required' => 0,
                'toolbar' => 'basic',
                'media_upload' => 0,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'page_template',
                    'operator' => '==',
                    'value' => 'page-services.php',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'active' => true,
        'description' => 'Fields for Trävaror page template',
    ));
}
