<?php

/**
 * ACF fields for Tjänster CPT
 */

// Function to populate staff choices
function populate_staff_choices($field)
{
    // Reset choices
    $field['choices'] = array();

    // Get staff list from options
    $staff_list = get_field('staff_list', 'option');

    if ($staff_list && is_array($staff_list)) {
        foreach ($staff_list as $index => $staff_member) {
            if (isset($staff_member['name'])) {
                $field['choices'][$index] = $staff_member['name'];
                if (isset($staff_member['title'])) {
                    $field['choices'][$index] .= ' - ' . $staff_member['title'];
                }
            }
        }
    }

    return $field;
}

add_filter('acf/load_field/key=field_your_staff_select', 'populate_staff_choices');


if (function_exists('acf_add_local_field_group')) {
    acf_add_local_field_group(array(
        'key' => 'group_services_fields',
        'title' => 'Tjänster Fields',
        'fields' => array(
            array(
                'key' => 'field_services_short_description',
                'label' => 'Kort Beskrivning',
                'name' => 'short_description',
                'type' => 'textarea',
                'instructions' => 'Kort beskrivning som visas på översiktssidan',
                'required' => 0,
                'rows' => 3,
                'placeholder' => 'Ange kort beskrivning...',
            ),
            array(
                'key' => 'field_services_specifications',
                'label' => 'Specifikationer',
                'name' => 'specifications',
                'type' => 'repeater',
                'instructions' => 'Lägg till produktspecifikationer',
                'required' => 0,
                'layout' => 'table',
                'button_label' => 'Lägg till specifikation',
                'sub_fields' => array(
                    array(
                        'key' => 'field_spec_name',
                        'label' => 'Namn',
                        'name' => 'name',
                        'type' => 'text',
                        'required' => 1,
                    ),
                    array(
                        'key' => 'field_spec_value',
                        'label' => 'Värde',
                        'name' => 'value',
                        'type' => 'text',
                        'required' => 1,
                    ),
                ),
            ),
            array(
                'key' => 'field_services_delivery_info',
                'label' => 'Leveransinformation',
                'name' => 'delivery_info',
                'type' => 'wysiwyg',
                'instructions' => 'Information om leverans och frakt',
                'required' => 0,
                'toolbar' => 'basic',
                'media_upload' => 0,
                'default_value' => 'Vi levererar över hela Sverige och internationellt. Kontakta oss för fraktinformation.',
            ),
            array(
                'key' => 'field_services_dimensions_info',
                'label' => 'Dimensionsinformation',
                'name' => 'dimensions_info',
                'type' => 'wysiwyg',
                'instructions' => 'Information om tillgängliga dimensioner',
                'required' => 0,
                'toolbar' => 'basic',
                'media_upload' => 0,
            ),
            array(
                'key' => 'field_services_production_time',
                'label' => 'Tillverkningstid',
                'name' => 'production_time',
                'type' => 'wysiwyg',
                'instructions' => 'Information om tillverkningstid',
                'required' => 0,
                'toolbar' => 'basic',
                'media_upload' => 0,
                'default_value' => 'Tillverkningstid varierar beroende på dimension och kvantitet. Kontakta oss för aktuell leveranstid.',
            ),
            array(
                'key' => 'field_services_af_info',
                'label' => 'ÅF Information',
                'name' => 'af_info',
                'type' => 'wysiwyg',
                'instructions' => 'Information om ÅF och kvalitetssorteringar',
                'required' => 0,
                'toolbar' => 'basic',
                'media_upload' => 0,
                'default_value' => 'Kvalitétssorteringar enligt C14 till C24 och TO till T3. Impregneringar i NTR-A och NTR-AB.',
            ),
            array(
                'key' => 'field_services_retailers',
                'label' => 'Återförsäljare',
                'name' => 'retailers',
                'type' => 'repeater',
                'instructions' => 'Lägg till återförsäljare',
                'required' => 0,
                'layout' => 'row',
                'button_label' => 'Lägg till återförsäljare',
                'sub_fields' => array(
                    array(
                        'key' => 'field_retailer_name',
                        'label' => 'Namn',
                        'name' => 'name',
                        'type' => 'text',
                        'required' => 1,
                    ),
                    array(
                        'key' => 'field_retailer_address',
                        'label' => 'Adress',
                        'name' => 'address',
                        'type' => 'textarea',
                        'rows' => 2,
                    ),
                    array(
                        'key' => 'field_retailer_phone',
                        'label' => 'Telefon',
                        'name' => 'phone',
                        'type' => 'text',
                    ),
                    array(
                        'key' => 'field_retailer_website',
                        'label' => 'Webbplats',
                        'name' => 'website',
                        'type' => 'url',
                    ),
                ),
            ),
            array(
                'key' => 'field_services_gallery',
                'label' => 'Bildgalleri',
                'name' => 'gallery',
                'type' => 'gallery',
                'instructions' => 'Lägg till bilder för produkten',
                'required' => 0,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
            ),
            array(
                'key' => 'field_services_contact_info',
                'label' => 'Kontaktinformation',
                'name' => 'contact_info',
                'type' => 'textarea',
                'instructions' => 'Specifik kontaktinformation för denna tjänst',
                'required' => 0,
                'rows' => 4,
            ),
            array(
                'key' => 'field_services_cta_title',
                'label' => 'CTA Titel',
                'name' => 'cta_title',
                'type' => 'text',
                'instructions' => 'Titel för CTA-sektionen',
                'required' => 0,
                'default_value' => 'Här kan jag handla',
                'placeholder' => 'Ange CTA titel...',
            ),
            array(
                'key' => 'field_services_cta_text',
                'label' => 'CTA Text',
                'name' => 'cta_text',
                'type' => 'textarea',
                'instructions' => 'Beskrivning för CTA-sektionen',
                'required' => 0,
                'rows' => 3,
                'default_value' => 'Upptäck vårt sortiment av trävaror och hitta rätt produkt för ditt projekt.',
            ),
            array(
                'key' => 'field_services_cta_button_text',
                'label' => 'CTA Knapptext',
                'name' => 'cta_button_text',
                'type' => 'text',
                'instructions' => 'Text för CTA-knappen',
                'required' => 0,
                'default_value' => 'Till ÅF',
            ),
            array(
                'key' => 'field_services_cta_button_link',
                'label' => 'CTA Länk',
                'name' => 'cta_button_link',
                'type' => 'post_object',
                'post_type' => array('page'),
                'return_format' => 'id',
                'ui' => 1,
                'instructions' => 'Välj sida att länka till',
                'required' => 0,
            ),
            array(
                'key' => 'field_services_cta_image',
                'label' => 'CTA Bild',
                'name' => 'cta_image',
                'type' => 'image',
                'instructions' => 'Bild för CTA-sektionen',
                'required' => 0,
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
            ),
            array(
                'key' => 'field_your_staff_select',
                'label' => 'Välj Personal',
                'name' => 'selected_staff',
                'type' => 'select',
                'instructions' => 'Välj personal från listan',
                'required' => 0,
                'choices' => array(), // Will be populated dynamically
                'allow_null' => 1,
                'multiple' => 0,
            ),


        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'services',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'active' => true,
        'description' => 'Fields for Tjänster CPT',
    ));
}
