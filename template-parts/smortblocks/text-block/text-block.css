.text-block {
  padding: 80px 0;
  background-color: var(--secondary-color);
  background-size: cover;
  background-position: center;
}

.text-block--accent {
  background-color: var(--accent-color);
}

.text-block--primary {
  background-color: var(--primary-color);
}

.text-block--primary .text-block__title,
.text-block--primary .text-block__content {
  color: var(--text-white);
}

.text-block__wrapper {
  max-width: 800px;
  margin: 0 auto;
}

.text-block__title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 2rem;
  line-height: 1.2;
}

.text-block__content {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-black);
}

.text-block__content p {
  margin-bottom: 1.5rem;
  font-size: 20px;
}

.text-block__content h3 {
  font-family: var(--font-heading);
  font-size: 1.8rem;
  font-weight: 600;
  margin: 2rem 0 1rem 0;
  color: var(--primary-color);
}

.text-block__content ul,
.text-block__content ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
  text-align: left;
  display: inline-block;
}

.text-block__content li {
  margin-bottom: 0.5rem;
  font-size: 18px;
}

@media (max-width: 768px) {
  .text-block {
    padding: 60px 0;
  }

  .text-block__title {
    margin-bottom: 1.5rem;
    font-size: 2rem;
  }

  .text-block__content p,
  .text-block__content li {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .text-block {
    padding: 40px 0;
  }
}
