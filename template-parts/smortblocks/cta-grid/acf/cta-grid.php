<?php

/**
 * ACF-fält för Cta Grid block
 */

if (function_exists('acf_add_local_field_group')) {
    acf_add_local_field_group(array(
        'key' => 'group_cta_grid_fields',
        'title' => 'Cta Grid Block Fields',
        'fields' => array(
            array(
                'key' => 'field_cta_grid_text_title',
                'label' => 'Title',
                'name' => 'title',
                'type' => 'text',
                'instructions' => 'Enter the title for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'Enter text...',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'field_cta_grid_text_subtitle',
                'label' => 'Subtitle',
                'name' => 'subtitle',
                'type' => 'text',
                'instructions' => 'Enter the subtitle for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'Enter text...',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'field_cta_grid_text_preamble',
                'label' => 'Preamble',
                'name' => 'preamble',
                'type' => 'text',
                'instructions' => 'Enter the preamble for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'Enter text...',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'field_cta_grid_url',
                'label' => 'Link',
                'name' => 'link',
                'type' => 'post_object',
                'post_type' => array('page'),
                'return_format' => 'id',
                'ui' => 1,
                'instructions' => 'Enter the cta for your block',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'https://example.com',
                'prepend' => '',
                'append' => '',
            ),
            array(
                'key' => 'field_cta_grid_repeater',
                'label' => 'Pages',
                'name' => 'pages',
                'type' => 'repeater',
                'instructions' => 'Add up to 3 page references',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'min' => 0,
                'max' => 3, // allow up to 3 references
                'layout' => 'row',
                'button_label' => 'Add Page Reference',
                'sub_fields' => array(
                    array(
                        'key' => 'field_cta_grid_page_select',
                        'label' => 'Select Page',
                        'name' => 'page',
                        'type' => 'post_object',
                        'post_type' => array('page'),
                        'return_format' => 'id', // or 'object'
                        'ui' => 1,
                    ),
                    array(
                        'key' => 'field_cta_grid_page_title',
                        'label' => 'Title',
                        'name' => 'title',
                        'type' => 'text',
                        'instructions' => 'Välj titel för bilden, sidas titel visas om ej angiven.',
                        'required' => 0,
                    ),
                    array(
                        'key' => 'field_cta_grid_page_excerpt',
                        'label' => 'Preambel',
                        'name' => 'preamble',
                        'type' => 'text',
                        'instructions' => 'Välj preambelö för kortet, sidas utdrag visas om ej angiven.',
                        'required' => 0,
                    ),
                    array(
                        'key' => 'fiel_cta_grid_page_image',
                        'label' => 'Image',
                        'name' => 'image',
                        'type' => 'image',
                        'instructions' => 'Välj bild för kortet, sidas bild visas om ej angiven.',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'return_format' => 'array',
                        'preview_size' => 'medium',
                        'library' => 'all',
                        'min_width' => '',
                        'min_height' => '',
                        'min_size' => '',
                        'max_width' => '',
                        'max_height' => '',
                        'max_size' => '',
                        'mime_types' => '',
                    ),
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/cta-grid',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => 'Fields for the Cta Grid block',
    ));
}
