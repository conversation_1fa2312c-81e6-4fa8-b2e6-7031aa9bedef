<?php

/**
 * Cta Grid Block Template
 * 
 * @param array $block The block settings and attributes.
 * @param string $content The block inner HTML (empty).
 * @param bool $is_preview True during AJAX preview.
 * @param int|string $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'cta-grid-' . $block['id'];
if (!empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$prefix = 'cta-grid';

$classes = $prefix;
if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}

// Load values and assign defaults.
$title = !empty(get_field('title')) ? get_field('title') : null;
$subtitle = !empty(get_field('subtitle')) ? get_field('subtitle') : null;
$preamble = !empty(get_field('preamble')) ? get_field('preamble') : null;
$link = !empty(get_field('link')) ? get_field('link') : null;
$pages = !empty(get_field('pages')) ? get_field('pages') : null;


// Get the CTA object from the id
$cta = !empty($link) ? get_post($link) : null;
// Get the Page objects from the ids
$pages = array_map(function ($page) {
    return get_post($page['page']);
}, $pages);

?>


<div class="container <?php echo esc_attr($classes); ?>">

    <section id="<?php echo esc_attr($id); ?>">

        <div class="<?= $prefix ?>-top">
            <div class="<?= $prefix ?>-top-left">

                <?php if (!empty($subtitle)) : ?>
                    <div class="<?= $prefix ?>-subtitle-container">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#1e4d2d" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-corner-down-right">
                            <polyline points="15 10 20 15 15 20"></polyline>
                            <path d="M4 4v7a4 4 0 0 0 4 4h12"></path>
                        </svg>

                        <p class="<?= $prefix ?>-subtitle"><?php echo esc_html($subtitle); ?></p>
                    </div>
                <?php endif; ?>

                <?php if (!(empty($title))) : ?>
                    <h2 class="<?= $prefix ?>-title"><?php echo esc_html($title); ?></h2>
                <?php endif; ?>

            </div>
            <div class="<?= $prefix ?>-top-right">
                <?php if (!empty($preamble)) : ?>
                    <p class="<?= $prefix ?>-preamble"><?php echo esc_html($preamble); ?></p>
                <?php endif; ?>
                <?php if (!empty($cta)) : ?>
                    <?php
                    _sc('button', [
                        'href' => get_permalink($cta),
                        'text' => $cta->post_title,
                    ]); ?>
                <?php endif; ?>
            </div>

        </div>


        <div class="<?= $prefix ?>-bottom">
            <?php foreach ($pages as $page) : ?>
                <div class="<?= $prefix ?>-item">
                    <img src="<?php echo get_the_post_thumbnail_url($page); ?>" alt="<?php echo $page->post_title; ?>" class="<?= $prefix ?>-item-image">
                    <div class="<?= $prefix ?>-item-content">
                        <?php if (!empty($page->post_title)) : ?>
                            <h3><?php echo $page->post_title; ?></h3>
                        <?php endif; ?>

                        <?php if (!empty(get_the_excerpt($page))) : ?>
                            <p><?php echo get_the_excerpt($page); ?></p>
                        <?php endif; ?>
                        <?php if (!empty(get_permalink($page))) : ?>
                            <?php _sc('button', [
                                'href' => get_permalink($page),
                                'text' => 'Läs mer',
                                'type' => 'accent',
                            ]); ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </section>

</div>