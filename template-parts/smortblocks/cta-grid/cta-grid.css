/**
 * Cta Grid Block Styles
 */

.cta-grid {
    /* Main block container */
    padding-top: 6rem;
    padding-bottom: 6rem;
    background: url('../../../img/smalandtimberbg.png');
    background-color: var(--accent-color);
}

.cta-grid-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: var(--spacing-lg);

    .cta-grid-top-left, .cta-grid-top-right {
        width: 50%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
}

.cta-grid-subtitle-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin:0;
    img {
        height: 20px;
        width: 20px;
        color: var(--primary-color);
    }
    .cta-grid-subtitle {
        font-family: var(--font-body);
        margin:0;
        color: var(--primary-color);
    }
}
.cta-grid-title {
    font-size: 4rem;
    margin:0;
    color: var(--primary-color);
}    
.cta-grid-preamble {
    font-family: var(--font-body);
}    

.cta-grid-bottom {
    margin-top: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-md);

    .cta-grid-item {
        aspect-ratio: 85/100;
        width: calc(33.33% - (2 * var(--spacing-md) / 3));
        position: relative;
        overflow: hidden;
        border-radius: var(--border-radius-medium);
        color: var(--black);
        padding: var(--spacing-md);
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        gap: var(--spacing-sm);
        
        &::after {
            content: '';
            position: absolute;
            inset: 0;
            background-color: rgba(0, 0, 0, 0.3);
            z-index: 5;
        }

        .cta-grid-item-image {
            position:absolute;
            object-fit:cover;
            width: 100%;
            height: 100%;
            inset:0;
        }


        .cta-grid-item-content {
            color: var(--white);
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            gap: var(--spacing-sm);
            z-index: 10;
            bottom:0;

            h3 {
                font-size: 2rem;
            }

            p {
                font-family: var(--font-body);
                font-size: 0.75rem;
            }

        }
    }


    @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-sm);

        .cta-grid-item {
            aspect-ratio: 1/1;
            width: 100%;
        }
    }

}


/* Responsive styles */
@media (max-width: 768px) {
    .cta-grid-top {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;

        .cta-grid-top-left, .cta-grid-top-right {
            width: 100%;
        }
    }
    .cta-grid-title {
        font-size: 2rem;
    }
    .cta-grid-bottom {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

