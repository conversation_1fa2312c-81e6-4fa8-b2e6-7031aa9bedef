/**
 * Fifty Fifty Callouts Block Styles
 */
.fifty-fifty-callouts {
  display: flex;
  gap: var(--spacing-lg);
  padding-block: var(--spacing-lg);
}
.cta-grid:has(+ .fifty-fifty-callouts) {
  padding-bottom: 30%;
}
.fifty-fifty-callouts:has(+ .cta-section) {
  margin-bottom: -25%;
}
.cta-grid + .fifty-fifty-callouts {
  margin-top: -25%;
}
.fifty-fifty-callouts + .cta-section {
  padding-top: 30%;
}

.fifty-fifty-callouts-image-callout,
.fifty-fifty-callouts-card-callout {
  aspect-ratio: 1 / 1;
  width: 50%;
  display: flex;
  gap: var(--spacing-sm);
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-lg) var(--spacing-md);

  display: flex;
  flex-direction: column;
}
.fifty-fifty-callouts-image-callout {
  justify-content: space-between;

  .fifty-fifty-callouts-image-callout-top {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--white);
    z-index: 5;
    font-family: var(--font-body);
    svg,
    path {
      stroke: var(--white);
      height: 20px;
      width: 20px;
    }
  }

  .fifty-fifty-callouts-image-callout-title {
    color: var(--white);
    font-size: 3rem;
    margin: 0;
    z-index: 5;
    max-width: 75%;
  }

  .fifty-fifty-callouts-image {
    position: absolute;
    object-fit: cover;
    width: 100%;
    height: 100%;
    inset: 0;
    z-index: 0;
  }
}

.fifty-fifty-callouts-card-callout {
  background-color: var(--secondary-color);
  justify-content: center;

  .fifty-fifty-callouts-card-callout-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);

    .fifty-fifty-callouts-card-callout-title {
      color: var(--primary-color);
      font-size: 3rem;
      margin: 0;
      text-align: center;
      text-decoration: underline;
      max-width: 16ch;
      margin: 0 auto;
    }
    .fifty-fifty-callouts-card-callout-preamble {
      font-family: var(--font-body);
      color: var(--primary-color);
      text-align: center;
      max-width: 75%;
    }
  }
}

/* Responsive styles */
@media (max-width: 1024px) {
  .fifty-fifty-callouts-card-callout-preamble {
    max-height: 110px;
    max-height: 4.6rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
}
@media (max-width: 768px) {
  .cta-grid:has(+ .fifty-fifty-callouts) {
    padding-bottom: 65%;
  }
  .fifty-fifty-callouts:has(+ .cta-section) {
    margin-bottom: -140%;
  }
  .cta-grid + .fifty-fifty-callouts {
    margin-top: -65%;
  }
  .fifty-fifty-callouts + .cta-section {
    padding-top: 150%;
  }

  .fifty-fifty-callouts {
    flex-direction: column-reverse;
  }
  .fifty-fifty-callouts-image-callout,
  .fifty-fifty-callouts-card-callout {
    aspect-ratio: 1 / 1;
    width: 100%;
  }

  .fifty-fifty-callouts-card-callout-preamble {
    max-width: 90%;
  }
}
@media (min-width: 1920px) {
  .fifty-fifty-callouts-card-callout-preamble {
    max-width: 55ch !important;
  }
}
