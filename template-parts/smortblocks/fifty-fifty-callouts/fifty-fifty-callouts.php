<?php

/**
 * Fifty Fifty Callouts Block Template
 *
 * @param array $block The block settings and attributes.
 * @param string $content The block inner HTML (empty).
 * @param bool $is_preview True during AJAX preview.
 * @param int|string $post_id The post ID this block is saved to.
 */

// Exit if ACF is not available
if (!function_exists('get_field')) {
    return;
}

// Create id attribute allowing for custom "anchor" value.
$id = 'fifty-fifty-callouts-' . $block['id'];
if (!empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$prefix = 'fifty-fifty-callouts';

// Get other classes
$classes = $prefix;
if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}

// Load values and assign defaults.
$page = !empty(get_field('page')) ? get_post(get_field('page')) : null;
$title_image = !empty(get_field('title_image')) ? get_field('title_image') : ($page && $page->post_title ? $page->post_title : null);
$title_card = !empty(get_field('title_card')) ? get_field('title_card') : ($page && $page->post_title ? $page->post_title : null);
$preamble_card = !empty(get_field('preamble_card')) ? get_field('preamble_card') : ($page && $page->post_excerpt ? $page->post_excerpt : null);
$image = !empty(get_field('image')) ? get_field('image') : ($page && has_post_thumbnail($page) ? get_the_post_thumbnail_url($page) : null);


?>

<?php if (!empty($page)) : ?>
    <section id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($classes); ?> container">
        <a href="<?php echo get_permalink($page); ?>" class="<?= $prefix ?>-image-callout">
            <img src="<?php echo esc_url($image)  ?>" alt="<?= $page->post_title; ?>" class="<?= $prefix ?>-image">
            <?php if (!empty($page->post_title)) : ?>
                <div class="<?= $prefix ?>-image-callout-top">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-corner-down-right">
                        <polyline points="15 10 20 15 15 20"></polyline>
                        <path d="M4 4v7a4 4 0 0 0 4 4h12"></path>
                    </svg>
                    <span><?= $page->post_title; ?></span>
                </div>
            <?php endif; ?>

            <?php if (!empty($title_image)) : ?>
                <h3 class="<?= $prefix ?>-image-callout-title"><?= $title_image; ?></h3>
            <?php endif; ?>
        </a>


        <div class="<?= $prefix ?>-card-callout">
            <div class="<?= $prefix ?>-card-callout-content">
                <?php if (!empty($title_card)) : ?>
                    <a href="<?php echo get_permalink($page); ?>">
                        <h3 class="<?= $prefix ?>-card-callout-title"><?= $title_card; ?></h3>
                    </a>
                <?php endif; ?>
                <?php if (!empty($preamble_card)) : ?>
                    <p class="<?= $prefix ?>-card-callout-preamble"><?= $preamble_card; ?></p>
                <?php endif; ?>

                <?php if (!empty(get_permalink($page)) && !empty($page->post_title)) : ?>
                    <?php _sc('button', [
                        'href' => get_permalink($page),
                        'text' => $page->post_title,
                        'type' => 'primary',
                    ]); ?>
                <?php endif; ?>
            </div>
        </div>

    </section>
<?php endif; ?>