<?php

/**
 * Fifty Fifty Callouts Block Template
 * 
 * @param array $block The block settings and attributes.
 * @param string $content The block inner HTML (empty).
 * @param bool $is_preview True during AJAX preview.
 * @param int|string $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'fifty-fifty-callouts-' . $block['id'];
if (!empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$prefix = 'fifty-fifty-callouts';

// Load values and assign defaults.
$title = !empty(get_field('text')) ? get_field('text') : null;
$content = !empty(get_field('textarea')) ? get_field('textarea') : null;

?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($classes); ?>">
    <div class="<?= $prefix ?>-inner">
        <?php if (!(empty($title))) : ?>
            <h2 class="<?= $prefix ?>-title"><?php echo esc_html($title); ?></h2>
            <?php endif; ?>()

            <?php if (!(empty($content))) : ?>
                <div class="<?= $prefix ?>-content">
                    <?php echo wp_kses_post($content); ?>
                </div>
            <?php endif; ?>
    </div>
</div>