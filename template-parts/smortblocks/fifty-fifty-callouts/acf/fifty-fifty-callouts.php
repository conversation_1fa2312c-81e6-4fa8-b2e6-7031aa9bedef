<?php

/**
 * ACF-fält för Fifty Fifty Callouts block
 */

if (function_exists('acf_add_local_field_group')) {
    acf_add_local_field_group(array(
        'key' => 'group_fifty_fifty_callouts_fields',
        'title' => 'Fifty Fifty Callouts Block Fields',
        'fields' => array(
            array(
                'key' => 'field_fifty_fifty_callouts_page',
                'label' => 'Sida',
                'name' => 'page',
                'type' => 'post_object',
                'instructions' => 'Välj sida som korten hänvisar till',
                'post_type' => array('page'),
                'return_format' => 'id',
                'ui' => 1,
                'required' => 1,
            ),
            array(
                'key' => 'field_fifty_fifty_callouts_text',
                'label' => 'Titel Bild',
                'name' => 'title_image',
                'type' => 'text',
                'instructions' => 'Välj titel för bilden, sidas titel visas om ej angiven.',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => 'Kärt barn har många namn',
                'placeholder' => 'Kärt barn har många namn',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'field_fifty_fifty_callouts_text',
                'label' => 'Titel Kort',
                'name' => 'title_card',
                'type' => 'text',
                'instructions' => 'Välj title för kortet, sidas titel visas om ej angiven.',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => 'Sparre, Bjälke, Stock, Balk',
                'placeholder' => 'Sparre, Bjälke, Stock, Balk',
                'default_value' => '',
                'placeholder' => 'Enter text...',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'field_fifty_fifty_callouts_preamble',
                'label' => 'Preamble Kort',
                'name' => 'preamble_card',
                'type' => 'text',
                'instructions' => 'Välj preamble för kortet, sidas utdrag visas om ej angiven.',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => 'Det enda som är fyrkantigt är vår bjälke. Endast 
fantasin begränsar funktionen.',
                'placeholder' => 'Det enda som är fyrkantigt är vår bjälke. Endast 
fantasin begränsar funktionen.',
                'default_value' => '',
            ),
            array(
                'key' => 'field_fifty_fifty_callouts_image',
                'label' => 'Image',
                'name' => 'image',
                'type' => 'image',
                'instructions' => 'Välj bild för kortet, sidas bild visas om ej angiven.',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'min_width' => '',
                'min_height' => '',
                'min_size' => '',
                'max_width' => '',
                'max_height' => '',
                'max_size' => '',
                'mime_types' => '',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/fifty-fifty-callouts',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => 'Fields for the Fifty Fifty Callouts block',
    ));
}
