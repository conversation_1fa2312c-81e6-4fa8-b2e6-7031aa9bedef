.about-block {
  padding: 80px 0;
  background-color: var(--accent-color);
}

.about-container {
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 60px;
  align-items: center;
}

.about-content {
  position: relative;
}

.about-text {
  position: relative;
  z-index: 2;
  display: flex;
}

.about-subtitle {
  color: var(--primary-color);
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
  display: block;
}

.about-title {
  font-family: var(--font-heading);
  font-size: 120px;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1.1;
  margin-bottom: 20px;
  margin-top: 0;
}

.about-text-container {
  margin-left: 20%;
  margin-top: auto;
  margin-bottom: auto;
}

.about-description {
  font-size: 25px;
  line-height: 1.6;
  color: var(--text-black);
  margin-bottom: 30px;
  opacity: 0.9;
}

.about-cta {
  display: inline-block;
  background-color: var(--primary-color);
  color: var(--text-white);
  padding: 12px 24px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  border: 2px solid var(--primary-color);
}

.about-cta:hover {
  background-color: transparent;
  color: var(--primary-color);
  transform: translateY(-2px);
}

.about-image {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.about-image img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  display: block;
}

@media (max-width: 768px) {
  .about-container {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .about-text {
    padding: 30px;
  }

  .about-image img {
    height: 300px;
  }

  .about-block {
    padding: 60px 0;
  }
}
