<?php
if (function_exists('acf_add_local_field_group')):

    acf_add_local_field_group(array(
        'key' => 'group_about_block',
        'title' => 'About Block Fields',
        'fields' => array(
            array(
                'key' => 'field_about_subtitle',
                'label' => 'About Subtitle',
                'name' => 'about_subtitle',
                'type' => 'text',
                'default_value' => 'Om oss',
                'placeholder' => 'Ange undertitel...',
            ),
            array(
                'key' => 'field_about_title',
                'label' => 'About Title',
                'name' => 'about_title',
                'type' => 'text',
                'default_value' => 'Fem Generationer',
                'placeholder' => 'Ange titel...',
            ),
            array(
                'key' => 'field_about_description',
                'label' => 'About Description',
                'name' => 'about_description',
                'type' => 'textarea',
                'rows' => 4,
                'default_value' => '<PERSON><PERSON>r kompetens sträcker sig långt bak och vår ambition långt fram. Vi har funnits ett tag nu, vi vet vad vi är och vart vi ska.',
                'placeholder' => 'Ange beskrivning...',
            ),
            array(
                'key' => 'field_about_cta_text',
                'label' => 'CTA Button Text',
                'name' => 'about_cta_text',
                'type' => 'text',
                'default_value' => 'Läs mer',
                'placeholder' => 'Ange knapptext...',
            ),
            array(
                'key' => 'field_about_cta_link',
                'label' => 'CTA Button Link',
                'name' => 'about_cta_link',
                'type' => 'url',
                'default_value' => '#',
                'placeholder' => 'https://...',
            ),
            array(
                'key' => 'field_about_image',
                'label' => 'About Image',
                'name' => 'about_image',
                'type' => 'image',
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'block',
                    'operator' => '==',
                    'value' => 'acf/about',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));

endif;
