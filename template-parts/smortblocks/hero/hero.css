/* Hero Block - Full Screen (Default) */
.hero-block {
  position: relative;
  height: 102vh;
  min-height: 500px;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--accent-color) 100%
  );
  background-image: url("img/smalandtimberbg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
  align-items: flex-end;
  padding-bottom: var(--spacing-3xl);
  border-bottom-left-radius: var(--border-radius-medium);
  border-bottom-right-radius: var(--border-radius-medium);

  .hero-content-bottom {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-end;
    justify-content: space-between;
  }
  .hero-cta {
    display: inline-block;
    background-color: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: 5px;
    text-decoration: none;
    font-family: var(--font-body);
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: 2px solid var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
  }
}

.hero-block .hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    50deg,
    rgba(30, 77, 45, 0.7) 0%,
    rgba(5, 5, 5, 0.3) 100%
  );
  z-index: 1;
}
.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  min-width: 100%;
  color: var(--text-white);
}

.hero-subtitle-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0;
}
.hero-subtitle-container svg {
  height: 20px;
  width: 20px;
  color: var(--text-white);
}
.hero-subtitle {
  font-family: var(--font-body);
  color: var(--text-white);
  font-weight: 600;
  max-width: 32ch;
}

.hero-title {
  font-family: var(--font-heading);
  font-size: var(--font-size-h0);
  color: var(--text-white);
  margin: 0;
  line-height: var(--line-height-headings);
  font-weight: 700;
}
/* Hero Block - Half Screen for Subpages */
.hero-block--half {
  height: 70vh;
  min-height: 500px;
  align-items: center;
  padding-bottom: 0;

  .hero-subtitle {
    font-family: var(--font-body);
    color: var(--text-white);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-tight);
  }
  .hero-title {
    font-size: var(--font-size-h2);
  }

  .hero-content {
    gap:var(--spacing-md);
  }
}



@media (max-width: 768px) {
  .hero-content-bottom {
    flex-direction: column;
  }
  .hero-block {
    /* height: 80vh; */
    min-height: 500px;
    align-items: flex-end;
  }

  .hero-block--half {
    height: 60vh;
    min-height: 300px;
    align-items: center;
    padding-bottom: 0;
  }

  .hero-content {
    max-width: 100%;
  }
