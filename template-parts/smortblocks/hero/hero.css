/* Hero Block - Full Screen (Default) */
.hero-block {
  position: relative;
  height: 90vh;
  min-height: 600px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 5%;
  overflow: hidden;
  align-items: end;
}

/* Hero Block - Half Screen for Subpages */
.hero-block--half {
  height: 50vh;
  min-height: 400px;
  align-items: center;
  padding-bottom: 0;
}

.hero-block::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    50deg,
    rgba(30, 77, 45, 0.7) 0%,
    rgba(5, 5, 5, 0.3) 100%
  );
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1000px;
  color: var(--text-white);
}

.hero-title {
  font-family: var(--font-heading);
  font-size: clamp(3rem, 8vw, 9rem);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 4rem;
}

/* Smaller title for half hero */
.hero-block--half .hero-title {
  font-size: clamp(3rem, 8vw, 6rem);
  margin-bottom: 2rem;
}

.hero-subtitle {
  font-family: var(--font-body);
  font-size: clamp(1.1rem, 2.5vw, 1.5rem);
  font-weight: 400;
  line-height: 1.4;
  margin-bottom: 3rem;
  opacity: 0.9;
  color: var(--text-white);
}

.hero-block--half .hero-subtitle {
  margin-bottom: 2rem;
}

.hero-cta {
  display: inline-block;
  background-color: var(--primary-color);
  color: var(--text-white);
  padding: 15px 30px;
  border-radius: 5px;
  text-decoration: none;
  font-family: var(--font-body);
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 2px solid var(--primary-color);
}

.hero-cta:hover {
  background-color: transparent;
  color: var(--text-white);
  border-color: var(--text-white);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .hero-block {
    height: 80vh;
    min-height: 500px;
    padding: 0 20px;
    align-items: flex-end;
    padding-bottom: 10%;
  }

  .hero-block--half {
    height: 60vh;
    min-height: 300px;
    align-items: center;
    padding-bottom: 0;
  }

  .hero-content {
    max-width: 100%;
  }
