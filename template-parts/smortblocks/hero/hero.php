<?php

$prefix = 'hero';
$hero_title = get_field('hero_title');
$hero_subtitle = get_field('hero_subtitle');
$hero_cta_text = get_field('hero_cta_text');
$hero_cta_link = get_field('hero_cta_link');
$hero_background = get_field('hero_background_image');
$hero_size = get_field('hero_size') ?: 'full'; // 'full' or 'half'

$background_style = '';
if ($hero_background) {
    $background_style = 'style="background-image: url(' . esc_url($hero_background['url']) . ');"';
}

$hero_class = $prefix . ' hero-block';
if ($hero_size === 'half') {
    $hero_class .= ' hero-block--half';
}

?>

<?php if ($hero_size == 'full') : ?>
    <section class="<?php echo $hero_class; ?>" <?php echo $background_style; ?>>
        <div class="<?= $prefix ?>-overlay" style="opacity: 70%;"></div>
        <div class="<?= $prefix ?>-content container">
            <h1 class="<?= $prefix ?>-title"><?php echo esc_html($hero_title); ?></h1>
            <div class="<?= $prefix ?>-content-bottom">
                <?php if (!empty($hero_subtitle)): ?>
                    <p class="<?= $prefix ?>-subtitle"><?php echo esc_html($hero_subtitle); ?></p>
                <?php endif; ?>
                <?php _sc('button', [
                    'href' => '#cta-grid-block',
                    'text' => 'Läs mer',
                    'type' => 'accent',
                ]); ?>
            </div>
        </div>
    </section>
<?php else : ?>
    <section class="<?php echo $hero_class; ?>" <?php echo $background_style; ?>>
        <div class="hero-overlay" style="opacity: 70%;"></div>
        <div class="container">
            <div class="<?= $prefix ?>-content">
                <?php if (!empty($hero_subtitle)): ?>
                    <div class="<?= $prefix ?>-subtitle-container">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-corner-down-right">
                            <polyline points="15 10 20 15 15 20"></polyline>
                            <path d="M4 4v7a4 4 0 0 0 4 4h12"></path>
                        </svg>
                        <span class="<?= $prefix ?>-subtitle"><?php echo esc_html($hero_subtitle); ?></span>
                    </div>
                <?php endif; ?>
                <h1 class="<?= $prefix ?>-title"><?php echo esc_html($hero_title); ?></h1>
            </div>

        </div>
    </section>
<?php endif; ?>