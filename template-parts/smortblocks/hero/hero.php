<?php
$hero_title = get_field('hero_title');
$hero_subtitle = get_field('hero_subtitle');
$hero_cta_text = get_field('hero_cta_text');
$hero_cta_link = get_field('hero_cta_link');
$hero_background = get_field('hero_background_image');
$hero_size = get_field('hero_size') ?: 'full'; // 'full' or 'half'

$background_style = '';
if ($hero_background) {
    $background_style = 'style="background-image: url(' . esc_url($hero_background['url']) . ');"';
}

$hero_class = 'hero-block';
if ($hero_size === 'half') {
    $hero_class .= ' hero-block--half';
}
?>

<section class="<?php echo $hero_class; ?>" <?php echo $background_style; ?>>
    <div class="hero-content">
        <h1 class="hero-title"><?php echo esc_html($hero_title); ?></h1>
        <?php if ($hero_subtitle): ?>
            <p class="hero-subtitle"><?php echo esc_html($hero_subtitle); ?></p>
        <?php endif; ?>
        <?php if ($hero_cta_link): ?>
            <a href="<?php echo esc_url($hero_cta_link); ?>" class="hero-cta">
                <?php echo esc_html($hero_cta_text); ?>
            </a>
        <?php endif; ?>
    </div>
</section>