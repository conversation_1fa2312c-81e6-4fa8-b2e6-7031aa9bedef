<?php

/**
 * Staff Block Template
 */

if (!function_exists('get_field')) {
    return;
}

// Sätt default för $block om den inte finns
if (!isset($block)) {
    $block = [
        'id' => uniqid('staff-'),
        'anchor' => '',
        'className' => '',
        'align' => ''
    ];
}

// Create id attribute
$id = 'staff-' . $block['id'];
if (!empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute
$classes = 'staff-block';
if (!empty($block['className'])) {
    $classes .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $classes .= ' align' . $block['align'];
}

// Get block fields
$title = get_field('staff_title') ?: 'Vår personal';
$subtitle = get_field('staff_subtitle') ?: 'Teamet';
$description = get_field('staff_description');

// Get staff from options page
$staff_list = get_field('staff_list', 'option');

// Ingen sortering behövs - ordningen bestäms av drag & drop i admin

?>

<section id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($classes); ?>">
    <div class="container">
        <div class="staff-header">

            <?php if ($title): ?>
                <h2 class="staff-title"><?php echo esc_html($title); ?></h2>
            <?php endif; ?>
        </div>

        <?php if ($staff_list): ?>
            <div class="staff-grid">
                <?php foreach ($staff_list as $staff_member): ?>
                    <div class="staff-card">
                        <?php if (!empty($staff_member['image'])): ?>
                            <div class="staff-image">
                                <img src="<?php echo esc_url($staff_member['image']['url']); ?>"
                                    alt="<?php echo esc_attr($staff_member['name']); ?>">
                            </div>
                        <?php endif; ?>

                        <div class="staff-content">
                            <h3 class="staff-name"><?php echo esc_html($staff_member['name']); ?></h3>

                            <?php if (!empty($staff_member['title'])): ?>
                                <p class="staff-job-title"><?php echo esc_html($staff_member['title']); ?></p>
                            <?php endif; ?>

                            <?php if (!empty($staff_member['description'])): ?>
                                <p class="staff-member-description"><?php echo esc_html($staff_member['description']); ?></p>
                            <?php endif; ?>

                            <div class="staff-contact">
                                <?php if (!empty($staff_member['phone'])): ?>
                                    <div class="staff-phone">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                                        </svg>
                                        <a href="tel:<?php echo esc_attr($staff_member['phone']); ?>">
                                            <?php echo esc_html($staff_member['phone']); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($staff_member['email'])): ?>
                                    <div class="staff-email">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                            <polyline points="22,6 12,13 2,6"></polyline>
                                        </svg>
                                        <a href="mailto:<?php echo esc_attr($staff_member['email']); ?>">
                                            <?php echo esc_html($staff_member['email']); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>