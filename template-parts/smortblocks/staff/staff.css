.staff-block {
  padding: 80px 0;
  background-color: var(--secondary-color);
}

.staff-header {
  text-align: center;
  margin-bottom: 60px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.staff-subtitle-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: 1rem;
}

.staff-subtitle {
  color: var(--primary-color);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.staff-title {
  color: var(--primary-color);
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  margin-bottom: 20px;
}

.staff-description {
  color: var(--text-gray);
  font-size: var(--font-size-p);
  line-height: 1.6;
  margin-bottom: 0;
}

.staff-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin: 0 auto;
}

.staff-card {
  background: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
}

.staff-image {
  position: relative;
  width: 100%;
  aspect-ratio: 4 / 5;
  overflow: hidden;
  background: var(--accent-color);
  height: auto; /* Ta bort eller skriv över eventuell höjd */
}

.staff-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.staff-card:hover .staff-image img {
  transform: scale(1.05);
}

.staff-content {
  padding: 30px;
}

.staff-name {
  color: var(--primary-color);
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-bold);
  margin-bottom: 8px;
  line-height: 1.2;
}

.staff-job-title {
  color: var(--primary-color);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 15px;
}

.staff-member-description {
  color: var(--text-gray);
  font-size: var(--font-size-small);
  line-height: 1.5;
  margin-bottom: 20px;
}

.staff-contact {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.staff-phone,
.staff-email {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-gray);
  font-size: var(--font-size-small);
}

.staff-phone svg,
.staff-email svg {
  color: var(--primary-color);
  flex-shrink: 0;
}

.staff-phone a,
.staff-email a {
  color: var(--text-gray);
  text-decoration: none;
  transition: color 0.3s ease;
}

.staff-phone a:hover,
.staff-email a:hover {
  color: var(--primary-color);
}

/* Responsiv design */
@media (max-width: 768px) {
  .staff-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .staff-header {
    margin-bottom: 40px;
  }

  .staff-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .staff-image {
    height: auto;
  }

  .staff-content {
    padding: 25px 20px;
  }
}

@media (max-width: 480px) {
  .staff-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .staff-image {
    height: auto;
  }

  .staff-content {
    padding: 20px 15px;
  }
}
