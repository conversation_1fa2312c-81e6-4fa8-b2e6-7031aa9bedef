<?php
/*
Template Name: Services
*/



get_header();

wp_enqueue_script('category-swiper-js', get_stylesheet_directory_uri() . '/js/category-swiper.js', array(), '1.0', true);
?>

<main class="services-page">
    <?php
    // Hero section for the page
    if (have_posts()) : while (have_posts()) : the_post();
            // Get ACF fields
            $hero_title = get_field('services_hero_title') ?: get_the_title();
            $hero_subtitle = get_field('services_hero_subtitle') ?: 'Våra produkter';
            $hero_description = get_field('services_hero_description') ?: get_the_content();
            $hero_background = get_field('services_hero_background');
            $hero_overlay_opacity = get_field('services_hero_overlay_opacity') ?: 70;

            // Set background style
            $background_style = '';
            if ($hero_background) {
                $background_style = 'style="background-image: url(' . esc_url($hero_background['url']) . ');"';
            }
    ?>
            <section class="services-hero" <?php echo $background_style; ?>>
                <div class="hero-overlay" style="opacity: <?php echo $hero_overlay_opacity / 100; ?>;"></div>
                <div class="container">
                    <div class="services-hero-content">
                        <div class="services-subtitle-container">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-corner-down-right">
                                <polyline points="15 10 20 15 15 20"></polyline>
                                <path d="M4 4v7a4 4 0 0 0 4 4h12"></path>
                            </svg>
                            <span class="services-subtitle"><?php echo esc_html($hero_subtitle); ?></span>
                        </div>
                        <h1 class="services-title"><?php echo esc_html($hero_title); ?></h1>
                        <?php if ($hero_description) : ?>
                            <div class="services-description">
                                <?php echo wp_kses_post($hero_description); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </section>
    <?php endwhile;
    endif; ?>

    <section class="services-intro">
        <div class="container">
            <div class="services-intro-wrapper">
                <div class="services-intro-content">
                    <h2 class="services-intro-title">
                        <?php echo esc_html(get_field('services_intro_title') ?: 'Våra trävaror'); ?>
                    </h2>
                    <div class="services-intro-text">
                        <?php
                        $intro_text = get_field('services_intro_text');
                        if ($intro_text) {
                            echo wp_kses_post($intro_text);
                        }
                        ?>
                    </div>
                </div>
                <div class="services-intro-image">
                    <?php
                    $intro_image = get_field('services_intro_image');
                    if ($intro_image) : ?>
                        <img src="<?php echo esc_url($intro_image['url']); ?>" alt="<?php echo esc_attr($intro_image['alt'] ?: 'Trävaror från Småland Timber'); ?>">
                    <?php else : ?>
                        <img src="<?php echo get_template_directory_uri(); ?>/img/services-intro.jpg" alt="Trävaror från Småland Timber">
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <?php

    // Hämta vald kategori från ACF
    $category_title = get_field('services_intro_category_section_title') ?: 'Kategorier';
    $selected_category = get_field('services_category_filter');
    $args = array(
        'post_type' => 'services',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    );

    if ($selected_category) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'services_category',
                'field' => 'term_id',
                'terms' => $selected_category,
            ),
        );
    }

    $services_query = new WP_Query($args);
    ?>

    <?php if ($services_query->have_posts()) : ?>
        <section class="services-services">
            <div class="container">
                <?php if (!empty($category_title)) : ?>
                    <h2 class="services-category-title"><?php echo esc_html($category_title); ?></h2>
                <?php endif; ?>
                <div class="services-services-swiper">
                    <?php
                    $count = 0;
                    while ($services_query->have_posts()) :
                        $services_query->the_post();
                        $count++;
                        $service_image = get_the_post_thumbnail_url(get_the_ID(), 'full');
                        $short_description = get_field('short_description');
                        $service_excerpt = $short_description ?: (get_the_excerpt() ?: "");
                    ?>
                        <div class="services-service-card">
                            <article class="services-service-card-link">
                                <?php if ($service_image) : ?>
                                    <img class="service-card-image" src="<?php echo esc_url($service_image); ?>" alt="<?php the_title(); ?>">
                                    <div class="service-card-overlay"></div>
                                    <div class="service-card-content">
                                        <div class="service-card-top">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-corner-down-right">
                                                <polyline points="15 10 20 15 15 20"></polyline>
                                                <path d="M4 4v7a4 4 0 0 0 4 4h12"></path>
                                            </svg>
                                            <?php
                                            $terms = get_the_terms(get_the_ID(), 'services_category');
                                            if ($terms && !is_wp_error($terms)) : ?>
                                                <span class="service-card-category"><?= $terms[0]->name; ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="service-card-bottom">
                                            <h3 class="service-card-title"><?php the_title(); ?></h3>
                                            <?php if ($service_excerpt) : ?>
                                                <p class="service-card-excerpt"><?php echo esc_html($service_excerpt); ?></p>
                                            <?php endif; ?>
                                            <?php _sc('button', [
                                                'href' => get_permalink(),
                                                'text' => 'Läs mer',
                                                'type' => 'accent',
                                                'aria-label' => 'Läs mer om ' . get_the_title()
                                            ]); ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </article>
                        </div>
                    <?php endwhile; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <?php wp_reset_postdata(); ?>


    <?php if (have_rows('hotspots')): ?>
        <?php
        $image = get_field('hotspot_image');
        $hotspots = get_field('hotspots');
        if ($image):
        ?>
            <div class="hotspot-block">
                <div class="container">
                    <div class="hotspot-content-wrapper">

                        <div class="hotspot-image-wrapper">
                            <img src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" class="hotspot-image" />
                            <?php if (have_rows('hotspots')): ?>
                                <?php while (have_rows('hotspots')): the_row(); ?>
                                    <button
                                        class="hotspot-dot"
                                        style="left:<?php echo floatval(get_sub_field('x')); ?>%; top:<?php echo floatval(get_sub_field('y')); ?>%;"
                                        aria-label="<?php echo esc_attr(get_sub_field('hotspot_title')); ?>"
                                        tabindex="0">
                                        <span class="hotspot-dot-inner">
                                            <span class="hotspot-dot-plus">+</span>
                                        </span>
                                        <span class="hotspot-tooltip">
                                            <strong><?php echo esc_html(get_sub_field('hotspot_title')); ?></strong>
                                        </span>
                                    </button>
                                <?php endwhile; ?>
                            <?php endif; ?>
                        </div>
                        <div class="hotspot-text-content">
                            <h2 class="hotspot-title">
                                <?php echo esc_html(get_field('hotspot_title') ?: 'Trävaror i bra material'); ?>
                            </h2>
                            <div class="hotspot-description">
                                <?php
                                $hotspot_desc = get_field('hotspot_description');
                                if ($hotspot_desc) {
                                    echo wp_kses_post($hotspot_desc);
                                } else {
                                    echo '<p>Upptäck kvaliteten i våra trävaror genom att klicka på punkterna i bilden. Varje detalj är noggrant utvald för att ge dig den bästa produkten för ditt projekt.</p>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>


    <?php if (have_rows('services_faq')): ?>
        <section class="services-faq">
            <div class="container">
                <h2 class="services-faq-title">Vanliga frågor</h2>
                <div class="services-faq-list">
                    <?php if (have_rows('services_faq')): ?>
                        <?php while (have_rows('services_faq')): the_row(); ?>
                            <div class="faq-item">
                                <button class="faq-question" aria-expanded="false">
                                    <?php echo esc_html(get_sub_field('question')); ?>
                                    <span class="faq-toggle" aria-hidden="true">+</span>
                                </button>
                                <div class="faq-answer">
                                    <p><?php echo esc_html(get_sub_field('answer')); ?></p>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <p>Inga vanliga frågor är inlagda ännu.</p>
                    <?php endif; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>


    <?php wp_reset_postdata(); ?>
</main>

<?php get_footer(); ?>





<script>
    // Lägg i t.ex. main.js eller direkt före </body>
    document.querySelectorAll('.faq-question').forEach(btn => {
        btn.addEventListener('click', function() {
            const item = btn.closest('.faq-item');
            const open = item.classList.toggle('open');
            btn.setAttribute('aria-expanded', open ? 'true' : 'false');

            // Stäng andra öppna frågor om du vill ha "accordion"-effekt:
            document.querySelectorAll('.faq-item').forEach(other => {
                if (other !== item) {
                    other.classList.remove('open');
                    other.querySelector('.faq-question').setAttribute('aria-expanded', 'false');
                }
            });
        });
    });
</script>