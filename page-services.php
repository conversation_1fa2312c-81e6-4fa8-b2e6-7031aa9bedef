<?php
/*
Template Name: Services
*/

get_header(); ?>

<main class="services-page">
    <?php
    // Hero section for the page
    if (have_posts()) : while (have_posts()) : the_post();
            // Get ACF fields
            $hero_title = get_field('services_hero_title') ?: get_the_title();
            $hero_subtitle = get_field('services_hero_subtitle') ?: 'Våra produkter';
            $hero_description = get_field('services_hero_description') ?: get_the_content();
            $hero_background = get_field('services_hero_background');
            $hero_overlay_opacity = get_field('services_hero_overlay_opacity') ?: 70;

            // Set background style
            $background_style = '';
            if ($hero_background) {
                $background_style = 'style="background-image: url(' . esc_url($hero_background['url']) . ');"';
            }
    ?>
            <section class="services-hero" <?php echo $background_style; ?>>
                <div class="hero-overlay" style="opacity: <?php echo $hero_overlay_opacity / 100; ?>;"></div>
                <div class="container">
                    <div class="services-hero-content">
                        <div class="services-subtitle-container">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-corner-down-right">
                                <polyline points="15 10 20 15 15 20"></polyline>
                                <path d="M4 4v7a4 4 0 0 0 4 4h12"></path>
                            </svg>
                            <span class="services-subtitle"><?php echo esc_html($hero_subtitle); ?></span>
                        </div>
                        <h1 class="services-title"><?php echo esc_html($hero_title); ?></h1>
                        <?php if ($hero_description) : ?>
                            <div class="services-description">
                                <?php echo wp_kses_post($hero_description); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </section>
    <?php endwhile;
    endif; ?>

    <section class="services-intro">
        <div class="container">
            <div class="services-intro-wrapper">
                <div class="services-intro-content">
                    <h2 class="services-intro-title">Våra trävaror</h2>
                    <div class="services-intro-text">
                        <p>Småland Timber producerar sågade och hyvlade trävaror för den nationella och internationella marknaden</p>
                        <p>Den största delen produceras mot order, vi har möjlighet att anpassa oss efter specifika krav på produkt. Vi producerar ca. 30 000m3sv/år.</p>
                        <p>Vi erbjuder alla dimensioner i exaktkapade längder med millimeter precision.</p>
                        <p>Kvalitétssorteringar enligt C14 till C24 och TO till T3.<br>
                            Impregneringar i NTR-A och NTR-AB.</p>
                    </div>
                </div>
                <div class="services-intro-image">
                    <?php
                    $intro_image = get_field('services_intro_image');
                    if ($intro_image) : ?>
                        <img src="<?php echo esc_url($intro_image['url']); ?>" alt="<?php echo esc_attr($intro_image['alt'] ?: 'Trävaror från Småland Timber'); ?>">
                    <?php else : ?>
                        <img src="<?php echo get_template_directory_uri(); ?>/img/services-intro.jpg" alt="Trävaror från Småland Timber">
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <?php

    // Hämta vald kategori från ACF
    $selected_category = get_field('services_category_filter');
    $args = array(
        'post_type' => 'services',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    );

    if ($selected_category) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'services_category',
                'field' => 'term_id',
                'terms' => $selected_category,
            ),
        );
    }

    $services_query = new WP_Query($args);
    ?>

    <?php if ($services_query->have_posts()) : ?>
        <section class="services-services">
            <div class="container">
                <div class="services-services-grid">
                    <div class="services-services-row-1">
                        <?php
                        $count = 0;
                        while ($services_query->have_posts()) :
                            $services_query->the_post();
                            $count++;
                            $service_image = get_the_post_thumbnail_url(get_the_ID(), 'full');
                            $short_description = get_field('short_description');
                            $service_excerpt = $short_description ?: (get_the_excerpt() ?: wp_trim_words(get_the_content(), 20));

                            // Close first row after 3 posts
                            if ($count == 4) {
                                echo '</div><div class="services-services-row-2">';
                            }
                        ?>
                            <div class="services-service-card">
                                <a href="<?php the_permalink(); ?>" class="services-service-card-link">
                                    <?php if ($service_image) : ?>
                                        <div class="service-card-image" style="background-image: url('<?php echo esc_url($service_image); ?>');">
                                            <div class="service-card-overlay"></div>
                                            <div class="service-card-content">
                                                <div class="service-card-top">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-corner-down-right">
                                                        <polyline points="15 10 20 15 15 20"></polyline>
                                                        <path d="M4 4v7a4 4 0 0 0 4 4h12"></path>
                                                    </svg>
                                                    <span class="service-card-category"> <?= get_the_terms(get_the_ID(), 'services_category')[0]->name; ?></span>
                                                </div>
                                                <div class="service-card-bottom">
                                                    <h3 class="service-card-title"><?php the_title(); ?></h3>
                                                    <?php if ($service_excerpt) : ?>
                                                        <p class="service-card-excerpt"><?php echo esc_html($service_excerpt); ?></p>
                                                    <?php endif; ?>
                                                    <?php _sc('button', [
                                                        'href' => get_permalink(),
                                                        'text' => 'Läs mer',
                                                        'type' => 'accent',
                                                    ]); ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </a>
                            </div>
                        <?php endwhile; ?>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <section class="services-cta">
        <div class="container">
            <div class="services-cta-content">
                <h3 class="services-cta-title">Vid intresse</h3>
                <p class="services-cta-text">Kontakta oss så hjälper vi dig att hitta rätt produkt för ditt projekt.</p>
                <div class="services-cta-button">
                    <?php _sc('button', [
                        'href' => '/kontakt',
                        'text' => 'Kontakta oss',
                        'type' => 'accent',
                    ]); ?>
                </div>
            </div>
        </div>
    </section>

    <?php wp_reset_postdata(); ?>
</main>

<?php get_footer(); ?>