<!DOCTYPE html>
<html <?php language_attributes(); ?>>

<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title><?php wp_title('|', true, 'right'); ?></title>
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>

    <header class="smort-header" id="main-header">
        <div class="header-container">
            <div class="smort-logo">
                <a href="<?php echo esc_url(home_url('/')); ?>">
                    <?php $logo = get_field('logo', 'option'); ?>
                    <?php if ($logo): ?>
                        <img src="<?php echo esc_url($logo); ?>" alt="<?php bloginfo('name'); ?>">
                    <?php endif; ?>
                </a>
            </div>


            <div class="menu-container">
                <nav class="main-navigation">
                    <?php wp_nav_menu(array(
                        'theme_location' => 'header-menu',
                        'container' => false,
                        'menu_class' => 'header-menu'
                    )); ?>
                </nav>

                <div class="header-cta">
                    <img src="/wp-content/themes/your-theme/img/search-icon.svg" alt="Search" class="search-icon">
                    <a href="#" class="cta-button">Vårt sortiment</a>
                </div>

            </div>
            <div class="burger-menu" id="burger-menu">
                <span></span>
                <span></span>
                <span></span>
            </div>

        </div>

        <!-- Mobile Menu -->
        <nav class="mobile-nav" id="mobile-nav">
            <button class="close-nav" id="close-nav">&times;</button>
            <?php wp_nav_menu(array(
                'theme_location' => 'header-menu',
                'container' => false,
                'menu_class' => 'mobile-menu'
            )); ?>
            <div style="text-align: center; margin-top: 30px;">
                <a href="#" class="cta-button">Vårt sortiment</a>
            </div>
        </nav>
    </header>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const burgerMenu = document.getElementById('burger-menu');
            const mobileNav = document.getElementById('mobile-nav');
            const closeNav = document.getElementById('close-nav');
            const header = document.getElementById('main-header');

            // Mobile menu functionality
            burgerMenu.addEventListener('click', function() {
                mobileNav.classList.add('active');
            });

            closeNav.addEventListener('click', function() {
                mobileNav.classList.remove('active');
            });

            document.addEventListener('click', function(event) {
                if (!mobileNav.contains(event.target) && !burgerMenu.contains(event.target)) {
                    mobileNav.classList.remove('active');
                }
            });

            // Header scroll effect
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
        });
    </script>