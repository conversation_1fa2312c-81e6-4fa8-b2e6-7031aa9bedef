<!DOCTYPE html>
<html <?php language_attributes(); ?>>

<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://use.typekit.net/ecc8chj.css">
    <title><?php wp_title('|', true, 'right'); ?></title>
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>

    <header class="smort-header" id="main-header">
        <div class="header-container">
            <div class="smort-logo">
                <a href="<?php echo esc_url(home_url('/')); ?>">
                    <?php $logo = get_field('logo', 'option'); ?>
                    <?php if ($logo): ?>
                        <img src="<?php echo esc_url($logo); ?>" alt="<?php bloginfo('name'); ?>">
                    <?php endif; ?>
                </a>
            </div>


            <div class="menu-container">
                <nav class="main-navigation">
                    <?php wp_nav_menu(array(
                        'theme_location' => 'header-menu',
                        'container' => false,
                        'menu_class' => 'header-menu'
                    )); ?>
                </nav>

                <div class="header-cta">
                    <?php _sc('button', [
                        'text' => 'Vårt showroom',
                        'type' => 'primary',
                        'id' => 'showroom-modal-btn'
                    ]); ?>
                </div>

            </div>
            <div class="burger-menu" id="burger-menu">
                <span></span>
                <span></span>
                <span></span>
            </div>

        </div>

        <!-- Mobile Menu -->
        <nav class="mobile-nav" id="mobile-nav">
            <button class="close-nav" id="close-nav">&times;</button>
            <?php wp_nav_menu(array(
                'theme_location' => 'header-menu',
                'container' => false,
                'menu_class' => 'mobile-menu'
            )); ?>
            <div class="mobile-cta">
                <?php _sc('button', [
                    'text' => 'Vårt showroom',
                    'type' => 'primary',
                    'id' => 'showroom-modal-btn-mobile'
                ]); ?>
            </div>
        </nav>
    </header>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const burgerMenu = document.getElementById('burger-menu');
            const mobileNav = document.getElementById('mobile-nav');
            const closeNav = document.getElementById('close-nav');
            const header = document.getElementById('main-header');

            // Mobile menu functionality
            burgerMenu.addEventListener('click', function() {
                mobileNav.classList.add('active');
            });

            closeNav.addEventListener('click', function() {
                mobileNav.classList.remove('active');
            });

            document.addEventListener('click', function(event) {
                if (!mobileNav.contains(event.target) && !burgerMenu.contains(event.target)) {
                    mobileNav.classList.remove('active');
                }
            });

            // Header scroll effect
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });



            document.querySelectorAll('.header-menu li.menu-item-has-children > a').forEach(function(link) {
                link.addEventListener('click', function(e) {
                    // Endast på mobil (kan justeras vid behov)
                    if (window.innerWidth < 1024) {
                        e.preventDefault();
                        this.parentElement.classList.toggle('active');
                    }
                });
            });


            document.querySelectorAll('.mobile-menu li.menu-item-has-children > a').forEach(function(link) {
                link.addEventListener('click', function(e) {
                    if (window.innerWidth < 1024) {
                        e.preventDefault();
                        this.parentElement.classList.toggle('active');
                    }
                });
            });
        });
    </script>

    <!-- Showroom Modal -->
    <div class="showroom-modal" id="showroom-modal">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>Boka besök i vårt showroom</h3>
                <button class="modal-close" id="showroom-modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>Fyll i formuläret nedan så kontaktar vi dig för att boka ett besök.</p>
                <?php _sc('contact-form', ['id' => 'ce30de0']); ?>
            </div>
        </div>
    </div>

    <script>
        // Showroom modal functionality
        document.addEventListener('DOMContentLoaded', function() {
            const showroomBtn = document.getElementById('showroom-modal-btn');
            const showroomModal = document.getElementById('showroom-modal');
            const showroomClose = document.getElementById('showroom-modal-close');
            const modalOverlay = showroomModal.querySelector('.modal-overlay');
            const showroomBtnMobile = document.getElementById('showroom-modal-btn-mobile');

            // Öppna modal
            showroomBtn.addEventListener('click', function(e) {
                e.preventDefault();
                showroomModal.classList.add('active');
                document.body.classList.add('modal-open');
            });

            showroomBtnMobile.addEventListener('click', function(e) {
                e.preventDefault();
                showroomModal.classList.add('active');
                document.body.classList.add('modal-open');
            });

            // Stäng modal
            function closeModal() {
                showroomModal.classList.remove('active');
                document.body.classList.remove('modal-open');
            }

            showroomClose.addEventListener('click', closeModal);
            modalOverlay.addEventListener('click', closeModal);

            // Stäng med ESC
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && showroomModal.classList.contains('active')) {
                    closeModal();
                }
            });

            // Förhindra att modal stängs när man klickar inuti
            showroomModal.querySelector('.modal-content').addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });
    </script>