/*
 Theme Name:   Smort Commerce Child
 Theme URI:    https://smort.se
 Description:  Smort Commerce Child Theme
 Author:       Smort AB
 Author URI:   https://smort.se
 Template:     Smort_commerce
 Version:      1.0.0
*/

/* Import the parent theme's stylesheet */
@import url("../Smort_commerce/style.css");

/* Add your custom styles here */

/* Main */

/* Core */
:root {
  --primary-color: #1e4d2d;
  --secondary-color: #f3f8e4;
  --accent-color: #f9fbf3;
  --text-black: #212121;
  --text-white: #fff;
  --black: var(--text-black);
  --white: var(--text-white);

  /* EXTENDED COLOR PALETTE */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  --font-heading: "CustomHeadingFont", sans-serif;
  --font-body: "CustomContentFont", sans-serif;


  --border-radius-small: 4px;
  --border-radius-medium: 8px;
  --border-radius-large: 12px;

  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
}
* {
    box-sizing: border-box;
    interpolate-size: allow-keywords;
}
body {
    margin:0;
    padding:0;
}
article {
  margin-bottom: 0px;
}

#wpadminbar {
  display: none;
}

body {
  margin: 0;
}

p {
  font-size: 18px;
  line-height: 1.6;
  color: var(--textBlack);
  margin-top: 5px;
}

a:hover {
  color: var(--text-back);
}
a:active {
  color: var(--primary-color) !important;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  /* font-family: var(--fontFamily) !important; */
  font-weight: 600;
  color: var(--text-back);
  margin-block-end: 0;
  line-height: 1.5;
}
a {
  text-decoration: none;
}
p a {
  color: var(--text-back);
}

.container {
    max-width: 100%;
    padding-inline: 64px;

    @media (max-width: 1024px) {
        padding-inline: 32px;
    }

    @media (max-width: 768px) {
        padding-inline: 16px;
    }
}