/*
 Theme Name:   Smort Commerce Child
 Theme URI:    https://smort.se
 Description:  Smort Commerce Child Theme
 Author:       Smort AB
 Author URI:   https://smort.se
 Template:     smort_commerce
 Version:      1.0.0
*/

/* Import the parent theme's stylesheet */
@import url("../smort_commerce/style.css");

/* Add your custom styles here */

/* Main */

/* Core */
:root {
  --primary-color: #1e4d2d;
  --secondary-color: #f3f8e4;
  --accent-color: #f9fbf3;
  --text-black: #212121;
  --text-white: #fff;
  --black: var(--text-black);
  --white: var(--text-white);

  /* EXTENDED COLOR PALETTE */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  --font-heading: "beaufort-pro", serif;
  --font-body: "beaufort-pro", serif;

  --border-radius-small: 4px;
  --border-radius-medium: 8px;
  --border-radius-large: 12px;

  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 5rem;

  /* TYPOGRAPHY VARIABLES */
  --font-size-h1: clamp(3.5rem, 10vw, 7rem);
  --font-size-h2: clamp(2.8rem, 7vw, 5rem);
  --font-size-h3: clamp(2.2rem, 5vw, 3.2rem);
  --font-size-h4: clamp(1.7rem, 3vw, 2.4rem);
  --font-size-h5: clamp(1.3rem, 2vw, 1.7rem);
  --font-size-h6: clamp(1.1rem, 1.5vw, 1.3rem);
  --font-size-p: clamp(1.2rem, 1.2vw, 1.1rem);
  --font-size-small: clamp(0.95rem, 1vw, 1rem);

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;
}
* {
  box-sizing: border-box;
  interpolate-size: allow-keywords;
}
body {
  margin: 0;
  padding: 0;
  font-family: "beaufort-pro", serif;
  font-weight: 400;
  font-style: normal;
}
article {
  margin-bottom: 0px;
}

p {
  color: var(--text-black);
  font-size: var(--font-size-p);
  line-height: var(--line-height-relaxed);
  margin-top: 5px;
}
p strong {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-size: var(--font-size-h4);
}
a:hover {
  color: var(--text-back);
}
a:active {
  color: var(--primary-color) !important;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading) !important;
  font-weight: var(--font-weight-semibold);
  margin-block-end: 0;
  line-height: var(--line-height-tight);
  margin-top: 0;
}

h1 {
  font-size: var(--font-size-h1);
  font-weight: var(--font-weight-bold);
}

h2 {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
}

h3 {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-semibold);
}

h4 {
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-semibold);
}

h5 {
  font-size: var(--font-size-h5);
  font-weight: var(--font-weight-medium);
}

h6 {
  font-size: var(--font-size-h6);
  font-weight: var(--font-weight-medium);
}
a {
  text-decoration: none;
  color: var(--primary-color);
}


.container {
  max-width: 2000px;
  padding-inline: 64px;
  margin: 0 auto;

  @media (max-width: 1024px) {
    padding-inline: 32px;
  }

  @media (max-width: 768px) {
    padding-inline: 16px;
  }
}
