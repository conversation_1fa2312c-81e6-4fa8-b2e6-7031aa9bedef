/* Header Menu */
.smort-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: transparent;
  padding: 20px 0;
  z-index: 999;
  transition: all 0.3s ease;
}

/* Header styling for single tjänster pages */
.single-services .smort-header {
  background: var(--primary-color);
}

.smort-header.scrolled {
  background: linear-gradient(
    135deg,
    rgba(30, 77, 45, 0.95) 0%,
    rgba(30, 77, 45, 0.9) 100%
  );
  padding: 10px 0;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 0 20px;
  transition: all 0.3s ease;
}

.smort-logo {
  display: flex;
  align-items: center;
}

.smort-logo img {
  height: 70px;
  filter: brightness(0) invert(1);
  transition: all 0.3s ease;
}

.smort-header.scrolled .smort-logo img {
  height: 60px;
}

.smort-header.scrolled .smort-logo h1 {
  font-size: 1.3rem;
}

.main-navigation {
  display: flex;
  align-items: center;
}

.main-navigation ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 15px;
}

.main-navigation ul li a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 700;
  font-size: 15px;
  padding: 8px 15px;
  border-radius: 20px;
  transition: all 0.3s ease;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.main-navigation ul li a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header-cta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.menu-container {
  background-color: var(--white);
  display: flex;
  padding: 1rem;
  border-radius: 10px;
  gap: 20px;
}

.smort-header.scrolled .menu-container {
  background-color: transparent;
}

.smort-header.scrolled .menu-container a {
  color: var(--text-white);
}

.cta-button {
  background-color: var(--primary-color);
  color: var(--text-white);
  padding: 10px 20px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  border: 2px solid var(--primary-color);
}

.cta-button:hover {
  background-color: transparent;
  border-color: var(--text-white);
  color: var(--text-white);
}

.search-icon {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1);
  cursor: pointer;
}

/* Mobile Menu */
.burger-menu {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 5px;
  width: 92%;
  align-items: end !important;
}

.burger-menu span {
  width: 25px;
  height: 3px;
  background-color: var(--text-white);
  margin: 3px 0;
  transition: 0.3s;
}

.mobile-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    rgba(30, 77, 45, 0.95) 100%
  );
  z-index: 2000;
  padding: 80px 20px 20px;
  overflow-y: auto;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  margin-top: 0 !important;
  width: 100% !important;
}

.mobile-nav.active {
  transform: translateX(0);
}

.mobile-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav ul li {
  margin: 20px 0;
}

.mobile-nav ul li a {
  color: var(--text-white);
  text-decoration: none;
  font-size: 1.5rem;
  font-weight: 500;
  display: block;
  padding: 15px;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.mobile-nav ul li a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.mobile-cta {
  display: flex;
  justify-content: center;

  & .button-primary {
    background-color: var(--text-white);
    color: var(--primary-color);
  }
}

.close-nav {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  color: var(--text-white);
  font-size: 30px;
  cursor: pointer;
}

/** Header Menu with Dropdown */
.header-menu li {
  position: relative;
}

.header-menu .sub-menu {
  display: block;
  position: absolute;
  left: 0;
  top: 110%;
  color: var(--text-black) !important;
  min-width: 200px;
  border-radius: 14px;
  box-shadow: 0 8px 32px rgba(30, 77, 45, 0.12), 0 1.5px 6px rgba(0, 0, 0, 0.04);
  z-index: 1000;
  padding: 10px 0;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1),
    top 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
}

.header-menu li:hover > .sub-menu,
.header-menu li.active > .sub-menu {
  display: block;
  opacity: 1;
  pointer-events: auto;
  top: 100%;
}

.header-menu .sub-menu li {
  width: 100%;
}

.header-menu .sub-menu li a {
  padding: 12px 28px;
  display: block;
  color: var(--primary-color);
  text-decoration: none;
  font-size: 15px;
  border-radius: 8px;
  transition: background 0.18s, color 0.18s;
  font-weight: 500;
  letter-spacing: 0.2px;
  color: var(--text-black) !important;
}

.header-menu li.menu-item-has-children > a::after {
  content: "";
  display: inline-block;
  margin-left: 7px;
  width: 14px;
  height: 14px;
  vertical-align: middle;
  background: url('data:image/svg+xml;utf8,<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 5L7 9L11 5" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>')
    no-repeat center center;
  background-size: 14px 14px;
  transition: transform 0.2s;
}

.header-menu li.menu-item-has-children:hover > a::after,
.header-menu li.menu-item-has-children.active > a::after {
  transform: rotate(180deg);
}

@media (max-width: 768px) {
  .main-navigation,
  .header-cta .cta-button {
    display: none;
  }

  .burger-menu {
    display: flex;
  }

  .menu-container {
    display: none;
  }

  .header-container {
    padding: 0 15px;
  }

  .smort-logo h1 {
    font-size: 1.2rem;
  }

  /* Justera header-container för mobil */
  .header-container {
    justify-content: space-between;
  }

  /* Säkerställ att hamburger-menyn är längst till höger */
  .header-cta {
    display: none;
  }

  .smort-logo img {
    height: 60px;
  }

  .smort-header.scrolled .smort-logo img {
    height: 40px;
  }
}

@media (min-width: 769px) {
  .burger-menu,
  .mobile-nav {
    display: none !important;
  }
}

.smort-header.scrolled .button-primary {
  background-color: var(--text-white);
  color: var(--primary-color) !important;
  border-color: var(--text-white);
}

.smort-header.scrolled .button-primary:hover {
  background-color: var(--text-white);
  color: var(--text-white);
  border-color: var(--text-white);
}

.smort-header.scrolled .button-primary .button-arrow svg {
  stroke: var(--text-white) !important;
}

.mobile-menu .sub-menu {
  display: none;
}
.mobile-menu li.active > .sub-menu {
  display: block;
}

@media (max-width: 768px) {
  .mobile-menu li.menu-item-has-children > a::after {
    content: "";
    display: inline-block;
    margin-left: 7px;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    background: url('data:image/svg+xml;utf8,<svg width="16" height="16" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 5L7 9L11 5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>')
      no-repeat center center;
    background-size: 16px 16px;
    transition: transform 0.2s;
  }
  .mobile-menu li.menu-item-has-children.active > a::after {
    transform: rotate(180deg);
  }
}

/* Showroom Modal */
.showroom-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.showroom-modal.active {
  opacity: 1;
  visibility: visible;
}

.showroom-modal .modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
}

.showroom-modal .modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  background: var(--secondary-color) !important;
  border-radius: var(--border-radius-large);
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transition: transform 0.3s ease;
  text-align: start;
}

.showroom-modal.active .modal-content {
  transform: translate(-50%, -50%) scale(1);
}

.showroom-modal .modal-header {
  padding: 30px 30px 20px;
  border-bottom: 1px solid rgba(30, 77, 45, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.showroom-modal .modal-header h3 {
  font-size: var(--font-size-h3);
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  margin: 0;
}

.showroom-modal .modal-close {
  background: var(--primary-color);
  border: none;
  font-size: 24px;
  color: var(--text-white);
  cursor: pointer;
  padding: 10px 15px;
  line-height: 1;
  transition: color 0.3s ease;
  border-radius: 9999px;
}

.showroom-modal .modal-close:hover {
  color: var(--primary-color);
}

.showroom-modal .modal-body {
  padding: 30px;
}

.showroom-modal .modal-body p {
  color: var(--text-black);
  margin-bottom: 25px;
  line-height: 1.6;
  font-size: 18px;
}

/* Contact Form 7 styling inom modal */
.showroom-modal .wpcf7-form {
  display: grid;
  gap: 20px;
}

.showroom-modal .wpcf7-form-control-wrap {
  position: relative;
}

.showroom-modal input[type="text"],
.showroom-modal input[type="email"],
.showroom-modal input[type="tel"],
.showroom-modal input[type="date"],
.showroom-modal input[type="time"],
.showroom-modal textarea,
.showroom-modal select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid rgba(30, 77, 45, 0.1);
  border-radius: var(--border-radius);
  font-size: var(--font-size-p);
  transition: border-color 0.3s ease;
  background: var(--white);
}

.showroom-modal input:focus,
.showroom-modal textarea:focus,
.showroom-modal select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.showroom-modal textarea {
  min-height: 100px;
  resize: vertical;
}

.showroom-modal .wpcf7-submit {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 15px 30px;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color 0.3s ease;
  justify-self: start;
}

.showroom-modal .wpcf7-submit:hover {
  background: var(--primary-dark);
}

/* Förhindra scrolling när modal är öppen */
body.modal-open {
  overflow: hidden;
}

/* Responsiv design */
@media (max-width: 768px) {
  .showroom-modal .modal-content {
    width: 95%;
    max-height: 95vh;
  }

  .showroom-modal .modal-header,
  .showroom-modal .modal-body {
    padding: 20px;
  }

  .showroom-modal .modal-header h3 {
    font-size: var(--font-size-h4);
  }
}
