/* Header Menu */
.smort-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: transparent;
  padding: 20px 0;
  z-index: 999;
  transition: all 0.3s ease;
}

.smort-header.scrolled {
  background: linear-gradient(
    135deg,
    rgba(30, 77, 45, 0.95) 0%,
    rgba(30, 77, 45, 0.9) 100%
  );
  padding: 10px 0;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 0 20px;
  transition: all 0.3s ease;
}

.smort-logo {
  display: flex;
  align-items: center;
}

.smort-logo img {
  height: 70px;
  filter: brightness(0) invert(1);
  transition: all 0.3s ease;
}

.smort-header.scrolled .smort-logo img {
  height: 60px;
}

.smort-header.scrolled .smort-logo h1 {
  font-size: 1.3rem;
}

.main-navigation {
  display: flex;
  align-items: center;
}

.main-navigation ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 15px;
}

.main-navigation ul li a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  padding: 8px 15px;
  border-radius: 20px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.main-navigation ul li a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.header-cta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.menu-container {
  background-color: var(--white);
  display: flex;
  padding: 1rem;
  border-radius: 10px;
}

.smort-header.scrolled .menu-container {
  background-color: transparent;
}

.smort-header.scrolled .menu-container a {
  color: var(--text-white);
}

.cta-button {
  background-color: var(--primary-color);
  color: var(--text-white);
  padding: 10px 20px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  border: 2px solid var(--primary-color);
}

.cta-button:hover {
  background-color: transparent;
  border-color: var(--text-white);
  color: var(--text-white);
}

.search-icon {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1);
  cursor: pointer;
}

/* Mobile Menu */
.burger-menu {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 5px;
  width: 90%;
  align-items: end !important;
}

.burger-menu span {
  width: 25px;
  height: 3px;
  background-color: var(--text-white);
  margin: 3px 0;
  transition: 0.3s;
}

.mobile-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    rgba(30, 77, 45, 0.95) 100%
  );
  z-index: 2000;
  padding: 80px 20px 20px;
  overflow-y: auto;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  margin-top: 0 !important;
  width: 91% !important;
}

.mobile-nav.active {
  transform: translateX(0);
}

.mobile-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav ul li {
  margin: 20px 0;
  text-align: center;
}

.mobile-nav ul li a {
  color: var(--text-white);
  text-decoration: none;
  font-size: 1.5rem;
  font-weight: 500;
  display: block;
  padding: 15px;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.mobile-nav ul li a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.close-nav {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  color: var(--text-white);
  font-size: 30px;
  cursor: pointer;
}

@media (max-width: 768px) {
  .main-navigation,
  .header-cta .cta-button {
    display: none;
  }

  .burger-menu {
    display: flex;
  }

  .menu-container {
    display: none;
  }

  .header-container {
    padding: 0 15px;
  }

  .smort-logo h1 {
    font-size: 1.2rem;
  }

  /* Justera header-container för mobil */
  .header-container {
    justify-content: space-between;
  }

  /* Säkerställ att hamburger-menyn är längst till höger */
  .header-cta {
    display: none;
  }
}

@media (min-width: 769px) {
  .burger-menu,
  .mobile-nav {
    display: none !important;
  }
}
