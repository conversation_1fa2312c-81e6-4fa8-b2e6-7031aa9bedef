.smort-footer {
  background-color: #05220e;
  color: var(--secondary-color);
  padding: 60px 0 20px;
  margin-top: 80px;
}

.footer-container {
  margin: 0 auto;
  padding: 0 50px;
  max-width: 1600px;
}

.footer-logo {
  border-right: 1px solid var(--white);
}

.footer-logo img {
  width: 400px;
}

.footer-content {
  display: grid;
  grid-template-columns: 4fr 1fr 1fr;
  gap: 50px;
  margin-bottom: 40px;
}

.d-flex {
  display: flex;
}

.footer-logo h2 {
  color: var(--text-white);
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 20px;
  border: 2px solid var(--text-white);
  padding: 10px 15px;
  display: inline-block;
}

.footer-description-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.footer-description {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 30px;
  opacity: 0.9;
  margin-left: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footer-contact-form h3 {
  color: var(--secondary-color);

  font-size: 1.2rem;
  margin-bottom: 15px;
}

.footer-contact-form .wpcf7-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-contact-form .wpcf7-form input[type="email"] {
  padding: 12px 15px;
  border: none;
  border-radius: 5px;
  font-size: 14px;
}

.footer-contact-form .wpcf7-form input[type="submit"] {
  background-color: var(--secondary-color);
  color: var(--primary-color);
  padding: 12px 20px;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.footer-contact-form .wpcf7-form input[type="submit"]:hover {
  background-color: rgba(255, 255, 255, 0.9);
}

.footer-links h4,
.footer-contact h4 {
  color: var(--secondary-color);
  font-size: 1.2rem;
  margin-bottom: 20px;
  font-weight: 600;
}

.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links ul li {
  margin-bottom: 10px;
}

.footer-links ul li a {
  color: var(--secondary-color);
  text-decoration: none;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.footer-links ul li a:hover {
  opacity: 1;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
  background-color: var(--secondary-color);
  padding: 15px;
  border-radius: 8px;
  color: var(--primary-color);
}

.contact-icon {
  font-size: 20px;
  margin-top: 2px;
}

.contact-info strong {
  display: block;
  font-weight: 600;
  margin-bottom: 5px;
}

.contact-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 20px;
  text-align: center;
}

.footer-bottom p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .smort-footer {
    padding: 40px 0 20px;
  }

  .footer-container {
    padding: 0 15px;
  }
}
