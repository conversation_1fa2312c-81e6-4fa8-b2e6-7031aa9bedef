.smort-footer {
  background-color: #05220e;
  color: var(--secondary-color);
  padding: 100px 0 20px;
}

.footer-container {
  margin: 0 auto;
  max-width: 1600px;
  padding: 0 2rem;
}

.footer-logo {
  border-right: 1px solid var(--white);
}

.footer-logo img {
  width: 400px;
}

.footer-content {
  display: grid;
  grid-template-columns: 4fr 1.2fr 2fr;
  margin-bottom: 40px;
  column-gap: var(--spacing-xl);
}

.footer-logo-text-container {
  display: flex;
}

.footer-logo h2 {
  color: var(--text-white);
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 20px;
  border: 2px solid var(--text-white);
  padding: 10px 15px;
  display: inline-block;
}

.footer-description-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.footer-description {
  font-size: var(--font-size-p);
  line-height: 1.6;
  margin-bottom: 30px;
  opacity: 0.9;
  margin-left: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-white);
}

.footer-contact-form {
  background-color: #13391f;
  border-radius: 10px;
  padding: 20px 20px;
  width: 80%;
  margin-top: 4rem;
}

.footer-contact-form h3 {
  color: var(--secondary-color);
  margin-top: 0;
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.footer-contact-form p {
  margin: 0;
}

.footer-contact-form .wpcf7-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-contact-form .wpcf7-form input[type="tel"] {
  padding: 12px 15px;
  border: none;
  border-radius: 5px;
  font-size: 14px;
  width: 100%;
}

.footer-contact-form .wpcf7-form input[type="submit"] {
  color: var(--white);
  padding: 12px 20px;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent;
}

.footer-contact-form .wpcf7-form input[type="submit"]::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='m22 2-7 20-4-9-9-4 20-7z'%3E%3C/path%3E%3Cpath d='m22 2-10 10'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  vertical-align: middle;
}

.footer-links {
  display: flex;
  /* align-items: center; */
  flex-direction: column;
}

.footer-links h4,
.footer-contact h4 {
  color: var(--secondary-color);
  font-size: 30px;
  margin-bottom: 20px;
  font-weight: 600;
  margin-top: 0;
}

.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links ul li {
  margin-bottom: 20px;
}

.footer-links ul li a {
  color: var(--secondary-color);
  text-decoration: none;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.footer-links ul li a:hover {
  opacity: 1;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  background-color: var(--secondary-color);
  padding: 15px;
  border-radius: 8px;
  color: var(--primary-color);
}

.contact-icon {
  font-size: 20px;
  margin-top: 2px;
}

.contact-info strong {
  font-family: var(--font-heading);
  display: block;
  font-weight: 600;
  margin-bottom: 5px;
  font-size: 22px;
}

.contact-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.footer-bottom {
  border-top: 0.5px solid var(--secondary-color);
  padding-top: 20px;
  text-align: center;
}

.footer-bottom p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

@media (max-width: 1100px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .footer-logo {
    border: none;
  }

  .footer-logo img {
    width: 300px;
    margin-bottom: 1rem;
    border: none;
  }

  .smort-footer {
    padding: 40px 0 20px;
  }

  .footer-description {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    margin-top: 1rem;
  }

  .footer-logo-text-container {
    display: block;
  }

  .footer-container {
    padding: 0 15px;
  }

  .footer-links {
    display: block;
  }

  .footer-contact-form {
    margin-top: 1rem;
    width: 100%;
  }
}
