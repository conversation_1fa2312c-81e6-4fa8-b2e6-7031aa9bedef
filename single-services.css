/* Single Tjänster Styles */
.service-single-page {
  background: var(--accent-color);
}

/* Hero Section */
.service-hero {
  color: var(--primary-color);
  padding-top: 12rem;
  background-image: url("img/smalandtimberbg.png");
  padding-bottom: var(--spacing-xl);

  @media (width > 1024px) {
    padding-bottom: 0;
    margin-bottom: -18%;
    & + .service-content {
      padding-top: 20%;
    }
    @media (width <= 768px) {
      margin-top: var(--spacing-4xl);
    }
  }
  }

.service-hero-content {
  padding-top: var(--spacing-xl);

  .product-slider {
    max-width : 50%;
    @media (width <= 1024px) {
      max-width: 100%;
    }
  }

  .main-image-wrapper {
    position: relative;
    padding-bottom: var(--spacing-sm);
    .slider-arrow {
      border: none;
      background-color: transparent;
      position:absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      cursor: pointer;

      &.prev {
        left: -1.25rem;
      }

      &.next {
        right: -1.25rem;
      }
    }
    
    .main-image-container {
      #main-image-link {
        display: block;
        width: 100%;
        height: auto;
      }
      #main-image {
        aspect-ratio: 3/2;
        object-fit: cover;
        width: 100%;
        height: auto;
        border-radius: var(--border-radius-medium);
      }
    }

  }

  .thumbnail-wrapper {
    display:flex;
    overflow-x:auto;
    gap: var(--spacing-sm);
    padding-bottom: var(--spacing-md);
    .thumbnail {
      width: 80px;
      height: 80px;
      aspect-ratio: 1/1;
      object-fit: cover;
      cursor: pointer;
      border-radius: var(--border-radius-medium);
    }
    .thumbnail.active {
      border: 2px solid var(--primary-color);
    }
  }


}
.service-hero-container {
  display: flex;
  justify-content: space-between;
  flex-direction: column;

  .service-hero-content {
    display: flex;
    gap: var(--spacing-2xl);
    align-items: stretch;

    .service-hero-image {
      max-width: 45%;
      border-radius: var(--border-radius-medium);
      object-fit: cover;

         @media (width <= 1024px) {
          max-width: 100%;
        }
      }
    
    .service-hero-text {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: var(--spacing-2xl);
      .service-hero-text-inner-bottom {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
      }
    }
    @media (width <= 1024px) {
      flex-direction: column;
      gap: var(--spacing-xl);
    }
  }
}

.service-breadcrumb {
  font-size: 0.9rem;
  margin-bottom: 1rem;
  color: var(--text-black);
}

.service-breadcrumb a {
  color: var(--primary-color);
  text-decoration: none;
}

.service-breadcrumb span {
  margin: 0 0.5rem;
}

.service-title {
  font-size: 5em;
  font-weight: 700;
  margin-bottom: 1rem;
}

.service-subtitle {
  font-size: 1.2rem;
  max-width: 600px;
}

/* Main Content Layout */
.service-content {
  padding: 3rem 0;
  background-color: var(--secondary-color);
}

.service-layout {
  display: grid;
  /* grid-template-columns: 3fr 2fr; */
  gap: 4rem;
  align-items: start;
}

/* Gallery */
.service-gallery {
  margin-top: var(--spacing-2xl);
  padding-bottom: var(--spacing-md);
  display: flex;
  max-width: 100%;
  gap: var(--spacing-lg);
  scroll-behavior: smooth;
  padding-inline: var(--spacing-lg);
  scrollbar-color: var(--primary-color) transparent;
  overflow-x: auto;

  .service-gallery-image-wrapper {
    flex: 0 0 auto;
    width: calc(30% - (2 * var(--spacing-lg) / 3));
    a {
      .service-gallery-image {
        border-radius: var(--border-radius-medium);
        width: 100%;
        aspect-ratio: 3/2;
        height: auto;
        min-height: 100%;
        object-fit: cover;
      }
    }
  }

  @media (width <= 1024px) {
    gap: var(--spacing-md);
    .service-gallery-image-wrapper {
      width: calc(40% - (2 * var(--spacing-md) / 3));

      @media (width <= 768px) {
        width: calc(80% - (2 * var(--spacing-md) / 3));
      }
    }
  }

  .service-gallery-image img {
    width: 100%;
    height: auto;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
}

.service-gallery-image img {
  width: 100%;
  height: auto;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-image a:hover img {
  transform: scale(1.05);
}

.product-thumbnails {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}
.product-ga .gallery-image {
  width: calc(25% - 0.5rem);
  height: auto;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;
}

/* Product Info */
.service-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.service-info h2 {
  font-size: 2.8rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.service-hero-text p {
  line-height: 1.6;
  margin-bottom: 0;

  word-break: auto-phrase;
}

.service-cta {
  padding: 2rem 0;
  border-top: 1px solid var(--primary-color);
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Tabs */
.service-tabs {
  border-radius: 5px;
}

.tab-nav {
  display: flex;
  border-bottom: 2px solid var(--secondary-color);
  overflow-x: auto;
}

.tab-btn {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  color: var(--primary-color);
}

.tab-btn.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  min-height: 200px;
  padding: var(--spacing-lg) 0;
}

.tab-pane {
  display: none;
  animation: fadeIn 0.3s ease;
}

.tab-pane.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Specifications Table */
.specifications-table {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);

}

.spec-row {
  display:flex;
  gap: var(--spacing-xl);
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: var(--text-white);
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
  width: fit-content;
  min-width: 40%;

  @media (width <= 768px) {
    min-width: 100%;
  }
  
}

.spec-name {
  font-weight: 600;
  color: var(--primary-color);
}

.spec-value {
  color: #495057;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .service-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .service-images {
    position: static;
  }

  .service-title {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .service-hero {
    padding: var(--spacing-2xl) 0 var(--spacing-lg) 0;
  }

  .service-title {
    font-size: 1.8rem;
  }

  .service-content {
    padding: 2rem 0;
  }

  .tab-nav {
    flex-wrap: wrap;
  }

  .tab-btn {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .modal-header,
  .modal-body {
    padding: 1.5rem;
  }

  .product-thumbnails {
    justify-content: center;
  }

  .thumbnail {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .spec-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .btn-large {
    width: 100%;
    justify-content: center;
  }
}

/* CTA Section */
.service-cta-section {
  background: var(--accent-color);
  padding: 4rem 0;
  margin-top: 2rem;
}

.service-cta-section-inner {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  max-width: 100%;

  @media (width <= 768px) {
    grid-template-columns: 1fr;
  }
}

.service-staff-member {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  color: var(--primary-color);


  color: var(--primary-color);
  .service-staff-image {
    border-radius: var(--border-radius-medium);
    overflow: hidden;
    object-fit: cover;
    aspect-ratio: 6/7;
    max-width : 320px;
  }

  .service-staff-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    color: var(--primary-color);
  }
}
.service-staff-wrapper {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  margin-top: 2rem;
}
.service-staff-name {
}

/* Contact form style */
/* Responsive for CTA Section */
@media (max-width: 1024px) {
  .service-cta-wrapper {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .service-cta-title {
    font-size: 2rem;
  }

  .service-cta-image img {
    height: 300px;
  }
  
  display: grid;
  grid-template-columns:  1fr 1fr;
}

@media (max-width: 768px) {
  .service-cta-section {
    padding: 3rem 0;
  }

  .service-cta-title {
    font-size: 1.8rem;
  }

  .service-cta-text {
    font-size: 1.1rem;
  }

  .service-cta-image img {
    height: 250px;
  }
}
