/**
 * Services Page Styles
 */

.services-hero {
  position: relative;
  height: 50vh;
  min-height: 500px;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--accent-color) 100%
  );
  background-image: url("img/smalandtimberbg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 5%;
  overflow: hidden;
}

.services-hero .hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    50deg,
    rgba(30, 77, 45, 0.7) 0%,
    rgba(5, 5, 5, 0.3) 100%
  );
  z-index: 1;
}

.services-hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-width: 800px;
  color: var(--text-white);
}

.services-subtitle-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0;
}

.services-subtitle-container svg {
  height: 20px;
  width: 20px;
  color: var(--text-white);
}

.services-subtitle {
  font-family: var(--font-body);
  color: var(--text-white);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.9;
}

.services-title {
  font-family: var(--font-heading);
  font-size: clamp(3rem, 8vw, 6rem);
  color: var(--text-white);
  margin: 0;
  line-height: 1.1;
  font-weight: 700;
}

.services-description {
  font-size: clamp(1.1rem, 2.5vw, 1.5rem);
  line-height: 1.6;
  color: var(--text-white);
  opacity: 0.9;
  font-weight: 400;
}

.services-services {
  padding: 6rem 0;
  background-color: var(--secondary-color);
}

.services-services-grid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-top: 2rem;
}

.services-services-row-1 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.services-services-row-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.services-service-card {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.services-service-card-link {
  display: block;
  text-decoration: none;
  color: inherit;
}

.service-card-image {
  position: relative;
  height: 500px;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: transform 0.3s ease;
}

.services-service-card:hover .service-card-image {
  transform: scale(1.05);
}

.service-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.2) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
  z-index: 1;
}

.service-card-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
  color: white;
}

.service-card-top {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.service-card-category {
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
}

.service-card-bottom {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.service-card-excerpt {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  margin: 0;
}

.services-service-card:hover .service-card-image img {
  transform: scale(1.05);
}

.services-service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.services-intro {
  background-image: url("img/smalandtimberbg.png");
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.services-intro-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.services-intro-content {
  max-width: none;
}

.services-intro-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 2rem;
  line-height: 1.2;
}

.services-intro-text {
  line-height: 1.6;
  color: var(--text-black);
}

.services-intro-text p {
  margin-bottom: 1.5rem;
  font-size: 20px;
}

.services-intro-image {
  position: relative;
}

.services-intro-image img {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-medium);
  object-fit: cover;
  aspect-ratio: 4/3;
}

.services-contact-info {
  background-color: var(--accent-color);
  padding: 1.5rem;
  border-radius: var(--border-radius-medium);
  margin-top: 2rem;
}

.services-contact-info p {
  margin: 0;
  color: var(--white);
}

.services-contact-info a {
  color: var(--secondary-color);
  text-decoration: none;
  font-weight: 600;
}

.services-contact-info a:hover {
  text-decoration: underline;
}

.services-cta {
  padding: 7rem 0;
  background-color: var(--primary-color);
  text-align: center;
}

.services-cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.services-cta-title {
  font-size: 3rem;
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-weight: 700;
  margin-top: 0;
}

.services-cta-text {
  font-size: 1.2rem;
  color: var(--secondary-color);
  margin-bottom: 2rem;
  opacity: 0.9;
}

.services-cta-button {
  display: flex;
  justify-content: center;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .services-services-row-1 {
    grid-template-columns: repeat(2, 1fr);
  }

  .services-services-row-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .services-hero {
    height: 60vh;
    min-height: 400px;
    padding: 0 20px;
    align-items: flex-end;
    padding-bottom: 10%;
  }

  .services-services-row-1,
  .services-services-row-2 {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .service-card-content {
    padding: 1.5rem;
  }

  .services-intro-title {
    font-size: 2rem;
  }

  .services-cta-title {
    font-size: 1.8rem;
  }

  .services-intro-text {
    font-size: 1rem;
  }

  .services-intro-wrapper {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .services-intro-image {
    order: -1;
  }
}

@media (max-width: 480px) {
  .services-services {
    padding: 4rem 0;
  }

  .service-card-image {
    height: 200px;
  }
}
