/**
 * Services Page Styles
 */

.services-hero {
  position: relative;
  height: 70vh;
  min-height: 500px;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--accent-color) 100%
  );
  background-image: url("img/smalandtimberbg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 5%;
  overflow: hidden;
  border-bottom-left-radius: var(--border-radius-medium);
  border-bottom-right-radius: var(--border-radius-medium);
}

.services-hero .hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    50deg,
    rgba(30, 77, 45, 0.7) 0%,
    rgba(5, 5, 5, 0.3) 100%
  );
  z-index: 1;
}

.services-hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-width: 800px;
  color: var(--text-white);
}

.services-subtitle-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0;
}

.services-subtitle-container svg {
  height: 20px;
  width: 20px;
  color: var(--text-white);
}

.services-subtitle {
  font-family: var(--font-body);
  color: var(--text-white);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-tight);
}

.services-title {
  font-family: var(--font-heading);
  font-size: var(--font-size-h2);
  color: var(--text-white);
  margin: 0;
  line-height: var(--line-height-headings);
  font-weight: 700;
}

.services-description {
  font-size: clamp(1.1rem, 2.5vw, 1.5rem);
  line-height: 1.6;
  color: var(--text-white);
  opacity: 0.9;
  font-weight: 400;
}

.services-description p {
  color: var(--text-white);
}

.services-services {
  padding-top: var(--spacing-4xl);
  padding-bottom: var(--spacing-3xl);
  background-color: var(--secondary-color);


  & > div {
    overflow-x: clip;
  }
}
.services-category-title {
  color: var(--primary-color);
  margin-bottom: clamp(var(--spacing-xl), 4.5vw, var(--spacing-2xl)) ;
  font-size: var(--font-size-h3);
}

.services-services-swiper {
  display:flex;
  gap: var(--spacing-lg);
  padding-block: var(--spacing-lg);
  overflow-x:auto;
  scroll-behavior: smooth;
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border-radius: 4px;
  }
  .services-service-card {
    flex: 0 0 calc(30% - var(--spacing-lg));

    @media (width <= 1024px) {
      flex: 0 0 calc(45% - var(--spacing-md));
    }
    @media (width <= 768px) {
      flex: 0 0 calc(90% - var(--spacing-md));
    }
    .service-card-image {
      position: absolute;
      inset:0;
      width:100%;
      height:100%;
      object-fit: cover;
    }
  }
}

.services-service-card {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  aspect-ratio: 85/100;
}

.services-service-card-link {
  display: block;
  text-decoration: none;
  color: inherit;
}

.service-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.2) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
  z-index: 1;
}

.service-card-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
  color: white;
}

.service-card-top {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.service-card-category {
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
}

.service-card-bottom {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  container-type: inline-size;
 
  .service-card-title, .service-card-excerpt {
    margin:0;
  }

  @container (width <= 270px) {
    .service-card-excerpt {
      display: none;
    }
    .button {
      min-width: fit-content;
    }
  }
}


.service-card-title {
  margin-bottom: 1rem;
  font-size: var(--font-size-h4);
}

.service-card-excerpt {
  color: var(--white);
  line-height: var(--line-height-normal);
  font-size: var(--font-size-small);
}

.services-service-card:hover .service-card-image img {
  transform: scale(1.05);
}

.services-service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.services-intro {
  background-image: url("img/smalandtimberbg.png");
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.services-intro-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.services-intro-content {
  max-width: none;
}

.services-intro-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 2rem;
  line-height: 1.2;
}

.services-intro-text {
  line-height: 1.6;
  color: var(--text-black);
}

.services-intro-text p {
  margin-bottom: 1.5rem;
  font-size: 20px;
}

.services-intro-image {
  position: relative;
}

.services-intro-image img {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-medium);
  object-fit: cover;
  aspect-ratio: 4/3;
}

.services-contact-info {
  background-color: var(--accent-color);
  padding: 1.5rem;
  border-radius: var(--border-radius-medium);
  margin-top: 2rem;
}

.services-contact-info p {
  margin: 0;
  color: var(--white);
}

.services-contact-info a {
  color: var(--secondary-color);
  text-decoration: none;
  font-weight: 600;
}

.services-contact-info a:hover {
  text-decoration: underline;
}

.services-cta {
  padding: 7rem 0;
  background-color: var(--primary-color);
  text-align: center;
}

.services-cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.services-cta-title {
  font-size: 3rem;
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-weight: 700;
  margin-top: 0;
}

.services-cta-text {
  font-size: 1.2rem;
  color: var(--secondary-color);
  margin-bottom: 2rem;
  opacity: 0.9;
}

.services-cta-button {
  display: flex;
  justify-content: center;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .services-services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .services-hero {
    height: 60vh;
    min-height: 400px;
    padding: 0 20px;
    padding-bottom: 10%;
  }

  .services-services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .service-card-content {
    padding: var(--spacing-lg);
  }

  .services-intro-title {
    font-size: 2rem;
  }

  .services-cta-title {
    font-size: 1.8rem;
  }

  .services-intro-text {
    font-size: 1rem;
  }

  .services-intro-wrapper {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .services-intro-image {
    order: -1;
  }
}

@media (max-width: 480px) {
  .services-services {
    padding: 4rem 0;
  }
}

.services-faq {
  background: var(--secondary-color);
  border-radius: var(--border-radius-large);
  padding: 6rem 0 10rem 0;
}

.services-faq-title {
  color: var(--primary-color);
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  text-align: center;
  margin-bottom: 2rem;
}

.services-faq-list {
  max-width: 700px;
  margin: 0 auto;
}

.faq-item + .faq-item {
  margin-top: 1.5rem;
}

.faq-question {
  width: 100%;
  background: var(--white);
  color: var(--primary-color);
  font-size: var(--font-size-h5);
  font-weight: var(--font-weight-semibold);
  border: none;
  border-radius: var(--border-radius-medium);
  padding: 1.2rem 1.5rem;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.faq-question[aria-expanded="true"] {
  background: var(--primary-color);
  color: var(--white);
}

.faq-toggle {
  font-size: 2rem;
  line-height: 1;
  margin-left: 1rem;
  transition: transform 0.2s;
}

.faq-question[aria-expanded="true"] .faq-toggle {
  transform: rotate(45deg);
}

.faq-answer {
  background: var(--white);
  border-radius: var(--border-radius-medium);
  margin-top: 0.5rem;
  padding: 1.2rem 1.5rem;
  color: var(--text-black);
  font-size: var(--font-size-p);
  height:0;
  visibility: hidden;
  overflow:hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  transition: height 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

.faq-item.open .faq-answer {
  height: auto;
  visibility: visible;
}

@media (max-width: 600px) {
  .services-faq {
    padding: 1.5rem 0;
  }
  .faq-question,
  .faq-answer {
    padding: 1rem;
    font-size: 1rem;
  }
}

.hotspot-block {
  position: relative;
  padding: 8rem 0;
  background: var(--accent-color);
  background-image: url("img/smalandtimberbg.png");
}

.hotspot-content-wrapper {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 6rem;
  align-items: center;
}

.hotspot-text-content {
  max-width: 500px;
}

.hotspot-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 2rem;
}

.hotspot-description {
  font-size: 1.2rem;
  line-height: 1.6;
  color: var(--text-black);
}

.hotspot-description p {
  margin: 0;
}

.hotspot-image-wrapper {
  position: relative;
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hotspot-image {
  display: block;
  aspect-ratio: 5/3;
  border-radius: var(--border-radius-large);
  margin: auto;
  width: 100%;
  max-width: 1200px;
  object-fit: cover;
}

.hotspot-dot {
  position: absolute;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  z-index: 2;
  width: 36px;
  height: 36px;
  transform: translate(-50%, -50%);
  outline: none;
  transition: transform 0.2s;
}

.hotspot-dot-inner {
  display: block;
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: 50%;
  border: 1px solid var(--accent-color);
  box-shadow: 0 2px 8px rgba(30, 77, 45, 1);
  transition: background 0.2s, box-shadow 0.2s;
  animation: hotspot-pulse 1.5s infinite;
}

.hotspot-dot-plus {
  color: var(--white);
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1;
  pointer-events: none;
  user-select: none;
  position: absolute;
  left: 56%;
  top: 56%;
  transform: translate(-50%, -50%);
}

@keyframes hotspot-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(30, 77, 45, 0.9);
  }
  70% {
    box-shadow: 0 0 0 16px rgba(30, 77, 45, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(30, 77, 45, 0);
  }
}

.hotspot-tooltip {
  display: block;
  position: absolute;
  left: 50%;
  top: -150%;
  transform: translateX(-50%);
  min-width: 115px;
  max-width: 260px;
  background: var(--white);
  color: var(--primary-color);
  border-radius: var(--border-radius-medium);
  box-shadow: 0 4px 24px rgba(30, 77, 45, 0.1);
  padding: 10px 8px;
  font-size: 1rem;
  font-family: var(--font-body);
  font-weight: var(--font-weight-medium);
  z-index: 10;
  pointer-events: none;
  opacity: 1;
  transition: opacity 0.2s;
}

@media (max-width: 1024px) {
  .hotspot-content-wrapper {
    grid-template-columns: 1fr;
    gap: 4rem;
    text-align: center;
  }

  .hotspot-text-content {
    max-width: none;
  }
}

@media (max-width: 768px) {
  .hotspot-block {
    padding: 4rem 0;
  }

  .hotspot-content-wrapper {
    gap: 3rem;
  }

  .hotspot-title {
    font-size: 2.5rem;
  }

  .hotspot-description {
    font-size: 1.1rem;
  }
}

@media (max-width: 600px) {
  .hotspot-block {
    max-width: 100%;
  }
  .hotspot-tooltip {
    font-size: 0.95rem;
    min-width: 120px;
    padding: 5px 2px;
    top: -100%;
  }

  .hotspot-dot-plus {
    display: none;
  }
  .hotspot-dot {
    width: 28px;
    height: 28px;
  }
  .hotspot-dot-inner {
    width: 20px;
    height: 20px;
  }
}
