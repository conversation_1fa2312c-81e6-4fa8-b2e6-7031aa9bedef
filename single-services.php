<?php

/**
 * Single Tjänster Template
 * Template for displaying individual tjänster (services/products)
 */


$prefix = 'service';
// Enqueue Swiper.js & CSS
wp_enqueue_style('swiper-css', 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css', array(), '11.0.0');
wp_enqueue_script('swiper-js', 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js', array(), '11.0.0', true);
// Enqueue Fancybox 4
wp_enqueue_style('fancybox-css', 'https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css', array(), '4.0.0');
wp_enqueue_script('fancybox-js', 'https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js', array(), '4.0.0', true);
// Custom slider init
wp_enqueue_script('service-gallery-js', get_stylesheet_directory_uri() . '/js/service-gallery.js', array('jquery', 'swiper-js', 'fancybox-js'), '1.2', true);
wp_enqueue_script('single-tjanster-js', get_stylesheet_directory_uri() . '/js/single-tjanster.js', array('jquery'), '1.0', true);

get_header(); ?>

<main class="<?= $prefix; ?>-single-page">
    <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
            <?php
            $gallery = get_field('gallery');
            $image_srcset = wp_get_attachment_image_srcset(get_post_thumbnail_id(get_the_ID()), 'full');
            $image_alt = get_post_meta(get_post_thumbnail_id(get_the_ID()), '_wp_attachment_image_alt', true);
            $image_url = get_the_post_thumbnail_url(get_the_ID(), 'full');
            $show_gallery = $gallery && count($gallery) > 0;
            ?>

            <!-- Hero Section -->
            <section class="<?= $prefix; ?>-hero">
                <div class="container <?= $prefix; ?>-hero-container">
                    <div class="<?= $prefix; ?>-hero-top">
                        <div class="<?= $prefix; ?>-breadcrumb">
                            <a href="<?php echo home_url('/services'); ?>">Tjänster</a>
                            <span>/</span>
                            <span><?php the_title(); ?></span>
                        </div>
                        <h1 class="<?= $prefix; ?>-title"><?php the_title(); ?></h1>

                    </div>
                    <div class="hero-cols">
                        <?php if (!empty($image_url)): ?>
                            <div class="product-slider-col">
                                <div class="swiper product-swiper-main">
                                    <div class="swiper-wrapper">
                                        <!-- Featured image -->
                                        <div class="swiper-slide">
                                            <a data-fancybox="gallery" data-caption="<?php echo esc_attr(get_the_title()); ?>" href="<?php echo esc_url($image_url); ?>">
                                                <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($image_alt); ?>" class="slider-image" />
                                            </a>
                                        </div>
                                        <?php if ($show_gallery): ?>
                                            <?php foreach ($gallery as $image):
                                                $full_url = wp_get_attachment_image_url($image['id'], 'full');
                                                $thumb_url = wp_get_attachment_image_url($image['id'], 'large');
                                                $caption = isset($image['caption']) ? $image['caption'] : '';
                                            ?>
                                                <div class="swiper-slide">
                                                    <a data-fancybox="gallery" data-caption="<?php echo esc_attr($caption); ?>" href="<?php echo esc_url($full_url); ?>">
                                                        <img src="<?php echo esc_url($thumb_url); ?>" alt="" class="slider-image" />
                                                    </a>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php if ($show_gallery): ?>
                                    <div class="swiper product-swiper-thumbs">
                                        <div class="swiper-wrapper">
                                            <!-- Featured image thumb -->
                                            <div class="swiper-slide">
                                                <a data-fancybox="gallery" data-caption="<?php echo esc_attr(get_the_title()); ?>" href="<?php echo esc_url($image_url); ?>">
                                                    <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($image_alt); ?>" class="slider-thumb-image" />
                                                </a>
                                            </div>
                                            <?php foreach ($gallery as $image):
                                                $thumb_url = wp_get_attachment_image_url($image['id'], 'thumbnail');
                                                $full_url = wp_get_attachment_image_url($image['id'], 'full');
                                                $caption = isset($image['caption']) ? $image['caption'] : '';
                                            ?>
                                                <div class="swiper-slide">
                                                    <a data-fancybox="gallery" data-caption="<?php echo esc_attr($caption); ?>" href="<?php echo esc_url($full_url); ?>">
                                                        <img src="<?php echo esc_url($thumb_url); ?>" alt="" class="slider-thumb-image" />
                                                    </a>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        <div class="hero-text-col">
                            <div class="<?= $prefix; ?>-hero-text-inner-top">
                                <?php the_content(); ?>
                            </div>
                            <div class="<?= $prefix; ?>-hero-text-inner-bottom">
                                <h4 class="<?= $prefix; ?>-hero-text-inner-bottom-title">Vill du veta mer?</h4>
                                <?php _sc('button', [
                                    'text' => 'Skicka förfrågan',
                                    'type' => 'primary',
                                    'href' => '#inquiry-form-wrapper-services'
                                ]); ?>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </section>

            <?php
            // Define tabs configuration
            $tabs = [
                'specifications' => [
                    'label' => 'Dimensioner',
                    'field' => 'specifications',
                    'always_show' => true,
                    'is_repeater' => true
                ],
                'production' => [
                    'label' => 'Tillverkningstid',
                    'field' => 'production_time',
                    'fallback' => 'Tillverkningstid varierar beroende på dimension och kvantitet. Kontakta oss för aktuell leveranstid.'
                ],
                'delivery' => [
                    'label' => 'Leverans',
                    'field' => 'delivery_info',
                    'fallback' => 'Vi levererar över hela Sverige och internationellt. Kontakta oss för fraktinformation.'
                ],
                'af' => [
                    'label' => 'ÅF',
                    'field' => 'af_info',
                    'fallback' => 'Kvalitétssorteringar enligt C14 till C24 och TO till T3. Impregneringar i NTR-A och NTR-AB.'
                ]
            ];

            // Filter tabs to only show those with content
            $active_tabs = array_filter($tabs, function ($tab) {
                if ($tab['is_repeater'] ?? false) {
                    return have_rows($tab['field']);
                }
                return $tab['always_show'] ?? !empty(get_field($tab['field']));
            });
            ?>

            <!-- Product Content -->
            <?php if (!empty($active_tabs)) : ?>

                <section class="<?= $prefix; ?>-content">
                    <div class="container">
                        <div class="<?= $prefix; ?>-layout">
                            <div class="<?= $prefix; ?>-info">
                                <h2>Information</h2>
                                <div class="<?= $prefix; ?>-tabs">
                                    <!-- Tab Navigation -->
                                    <div class="tab-nav">
                                        <?php foreach ($active_tabs as $tab_id => $tab) : ?>
                                            <button class="tab-btn <?= $tab_id === array_key_first($active_tabs) ? 'active' : ''; ?>"
                                                data-tab="<?= $tab_id; ?>">
                                                <?= esc_html($tab['label']); ?>
                                            </button>
                                        <?php endforeach; ?>
                                    </div>

                                    <!-- Tab Content -->
                                    <div class="tab-content">
                                        <?php foreach ($active_tabs as $tab_id => $tab) : ?>
                                            <div class="tab-pane <?= $tab_id === array_key_first($active_tabs) ? 'active' : ''; ?>"
                                                id="<?= $tab_id; ?>">
                                                <?php
                                                $field_value = get_field($tab['field']);

                                                if ($tab['is_repeater'] ?? false) {
                                                    // Handle repeater fields (specifications)
                                                    if (have_rows($tab['field'])) : ?>
                                                        <div class="specifications-table">
                                                            <?php while (have_rows($tab['field'])) : the_row(); ?>
                                                                <div class="spec-row">
                                                                    <span class="spec-name"><?= esc_html(get_sub_field('name')); ?></span>
                                                                    <span class="spec-value"><?= esc_html(get_sub_field('value')); ?></span>
                                                                </div>
                                                            <?php endwhile; ?>
                                                        </div>
                                                <?php endif;
                                                } else {
                                                    // Handle regular fields
                                                    if (!empty($field_value)) {
                                                        echo wp_kses_post($field_value);
                                                    } elseif (!empty($tab['fallback'])) {
                                                        echo '<p>' . esc_html($tab['fallback']) . '</p>';
                                                    }
                                                }
                                                ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <?= _sc('button', [
                                        'text' => 'Jag vill ha offert',
                                        'type' => 'primary',
                                        'href' => '#inquiry-form-wrapper-services'
                                    ]); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            <?php endif; ?>

    <?php endwhile;
    endif; ?>


    <!-- Reseller CTA Section -->
    <?php
    $cta_title = get_field('cta_title') ?: 'Bli återförsäljare';
    $cta_text = get_field('cta_text') ?: 'Vill du sälja våra produkter? Kontakta oss för att bli återförsäljare och få tillgång till vårt kompletta sortiment av trävaror.';
    $cta_button_text = get_field('cta_button_text') ?: 'Bli återförsäljare';
    $cta_image = get_field('cta_image');
    ?>
    <section class="reseller-cta-section">
        <div class="container">
            <div class="reseller-cta-wrapper">
                <?php if ($cta_image) : ?>
                    <div class="reseller-cta-image-col">
                        <img src="<?php echo esc_url($cta_image['url']); ?>"
                            alt="<?php echo esc_attr($cta_image['alt'] ?: $cta_title); ?>"
                            class="reseller-cta-image" />
                    </div>
                <?php endif; ?>

                <div class="reseller-cta-content-col">
                    <h3 class="reseller-cta-title"><?php echo esc_html($cta_title); ?></h3>
                    <p class="reseller-cta-text"><?php echo esc_html($cta_text); ?></p>

                    <?php _sc('button', [
                        'href' => '/aterforsal jare',
                        'text' => $cta_button_text,
                        'type' => 'primary',
                    ]); ?>
                </div>
            </div>
        </div>
    </section>

    <?php
    $selected_staff_index = get_field('selected_staff');
    $staff_member = array();
    if ($selected_staff_index !== null && $selected_staff_index !== '') {
        $staff_list = get_field('staff_list', 'option');

        if ($staff_list && isset($staff_list[$selected_staff_index])) {
            $staff_member = $staff_list[$selected_staff_index];
        }
    }
    ?>

    <!-- CTA Section -->
    <section class="<?= $prefix; ?>-cta-section">
        <div class="<?= $prefix; ?>-cta-section-inner container">
            <div class="<?= $prefix; ?>-staff-member">

                <h2 class="<?= $prefix; ?>-cta-title"> Vill du veta mer? </h2>
                <p class="<?= $prefix; ?>-cta-text">Vi finns här med personlig service och mångårig erfarenhet, kontakta oss idag så hjälper vi dig vidare med din frågeställning. Fyll i formuläret eller ring oss.</p>


                <?php if (!empty($staff_member)) : ?>
                    <div class="<?= $prefix; ?>-staff-wrapper">
                        <img class="<?= $prefix; ?>-staff-image" srcset="<?php echo wp_get_attachment_image_srcset($staff_member['image']['id'], 'full'); ?>" alt="<?php echo esc_attr($staff_member['name']); ?>" />
                        <div class="<?= $prefix; ?>-staff-content">
                            <h4 class="<?= $prefix; ?>-staff-name"><?= $staff_member['name']; ?></h4>
                            <a class="<?= $prefix; ?>-staff-phone" href="tel:<?= $staff_member['phone']; ?>"> <?= $staff_member['phone']; ?></a>
                        </div>
                    </div>
                <?php else : ?>
                    <?= _sc('button', [
                        'href' => '/kontakt',
                        'text' => 'Kontakta oss',
                        'type' => 'primary',
                    ]); ?>
                <?php endif; ?>


            </div>

            <?= _sc('contact-form', [
                'id' => 'b041fb0',
                'anchor' => 'services'
            ]); ?>
        </div>
    </section>

</main>

<?php get_footer(); ?>