<?php

/**
 * Single Tjänster Template
 * Template for displaying individual tjänster (services/products)
 */
$prefix = 'service';

// Enque scripts
wp_enqueue_script('service-gallery-js', get_stylesheet_directory_uri() . '/js/service-gallery.js', array('jquery', 'fancybox-js'), '1.0', true);
wp_enqueue_script('single-tjanster-js', get_stylesheet_directory_uri() . '/js/single-tjanster.js', array('jquery'), '1.0', true);

get_header(); ?>

<main class="<?= $prefix; ?>-single-page">
    <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
            <?php
            $gallery = get_field('gallery');
            $image_srcset = wp_get_attachment_image_srcset(get_post_thumbnail_id(get_the_ID()), 'full');
            $image_alt = get_post_meta(get_post_thumbnail_id(get_the_ID()), '_wp_attachment_image_alt', true);
            $image_url = get_the_post_thumbnail_url(get_the_ID(), 'full');
            $show_gallery = $gallery && count($gallery) > 0;
            ?>

            <!-- Hero Section -->
            <section class="<?= $prefix; ?>-hero">
                <div class="container <?= $prefix; ?>-hero-container">
                    <div class="<?= $prefix; ?>-hero-top">
                        <div class="<?= $prefix; ?>-breadcrumb">
                            <a href="<?php echo home_url('/services'); ?>">Tjänster</a>
                            <span>/</span>
                            <span><?php the_title(); ?></span>
                        </div>
                        <h1 class="<?= $prefix; ?>-title"><?php the_title(); ?></h1>

                    </div>
                    <div class="<?= $prefix; ?>-hero-content">

                        <?php if (!empty($image_url)): ?>

                            <div class="product-slider">
                                <div class="main-image-wrapper">
                                    <?php if ($show_gallery): ?>
                                        <button class="slider-arrow prev">&#10094;</button>
                                    <?php endif; ?>
                                    <div class="main-image-container">
                                        <!-- Huvudbild med Fancybox -->
                                        <a id="main-image-link" data-fancybox="product-gallery" href="<?php echo esc_url($image_url); ?>">
                                            <img id="main-image" src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($image_alt); ?>">
                                        </a>
                                    </div>
                                    <?php if ($show_gallery): ?>
                                        <button class="slider-arrow next">&#10095;</button>
                                    <?php endif; ?>
                                </div>


                                <?php if ($show_gallery): ?>
                                    <!-- Thumbnails -->
                                    <div class="thumbnail-wrapper">
                                        <!-- Featured image thumbnail -->
                                        <img class="thumbnail active"
                                            src="<?php echo esc_url($image_url); ?>"
                                            data-full="<?php echo esc_url($image_url); ?>">

                                        <!-- Gallery thumbnails -->
                                        <?php foreach ($gallery as $image):
                                            $full_url = wp_get_attachment_image_url($image['id'], 'full');
                                            $thumb_url = wp_get_attachment_image_url($image['id'], 'medium');
                                        ?>
                                            <img class="thumbnail"
                                                src="<?php echo esc_url($thumb_url); ?>"
                                                data-full="<?php echo esc_url($full_url); ?>">
                                        <?php endforeach; ?>
                                    </div>

                                <?php endif; ?>
                                <!-- Hidden fancybox links for gallery navigation -->
                                <div style="display: none;">
                                    <?php foreach ($gallery as $image): ?>
                                        <a href="<?php echo esc_url($image['url']); ?>" data-fancybox="product-gallery"></a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="<?= $prefix; ?>-hero-text">
                            <div class="<?= $prefix; ?>-hero-text-inner-top">
                                <?php the_content(); ?>
                            </div>
                            <div class="<?= $prefix; ?>-hero-text-inner-bottom">
                                <h4>Vill du veta mer? Tveka inte att skicka en förfrågan!</h4>
                                <?php _sc('button', [
                                    'text' => 'Skicka förfrågan',
                                    'type' => 'primary',
                                    'href' => '#inquiry-form-wrapper-services'
                                ]); ?>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </section>

            <?php 
        $specifications = get_field('specifications');
        $production_time = get_field('production_time');
        $delivery_info = get_field('delivery_info');
        $af_info = get_field('af_info');
    ?>
    <?php if ()
            <!-- Product Content -->
            <section class="<?= $prefix; ?>-content">
                <div class="container">
                    <div class="<?= $prefix; ?>-layout">
                        <div class="<?= $prefix; ?>-info">
                            <h2> Information</h2>
                            <!-- Tabs -->
                            <div class="<?= $prefix; ?>-tabs">
                                <div class="tab-nav">
                                    <button class="tab-btn active" data-tab="specifications">Dimensioner</button>
                                    <?php if (!empty(get_field('production_time'))) : ?>
                                        <button class="tab-btn" data-tab="production">Tillverkningstid</button>
                                    <?php endif; ?>
                                    <?php if (!empty(get_field('delivery_info'))) : ?>
                                        <button class="tab-btn" data-tab="delivery">Leverans</button>
                                    <?php endif; ?>
                                    <?php if (!empty(get_field('af_info'))) : ?>
                                        <button class="tab-btn" data-tab="af">ÅF</button>
                                    <?php endif; ?>
                                </div>

                                <div class="tab-content">
                                    <!-- Specifications Tab -->
                                    <div class="tab-pane active" id="specifications">
                                        <?php if (have_rows('specifications')) : ?>
                                            <div class="specifications-table">
                                                <?php while (have_rows('specifications')) : the_row(); ?>
                                                    <div class="spec-row">
                                                        <span class="spec-name"><?php echo esc_html(get_sub_field('name')); ?></span>
                                                        <span class="spec-value"><?php echo esc_html(get_sub_field('value')); ?></span>

                                                    </div>
                                                <?php endwhile; ?>
                                            </div>
                                        <?php else : ?>
                                            <p>Inga specifikationer tillgängliga.</p>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Production Time Tab -->
                                    <?php if (!empty(get_field('production_time'))) : ?>
                                        <div class="tab-pane" id="production">
                                            <?php echo wp_kses_post(get_field('production_time')); ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Delivery Tab -->
                                    <?php if (!empty(get_field('delivery_info'))) : ?>
                                        <div class="tab-pane" id="delivery">
                                            <?php echo wp_kses_post(get_field('delivery_info')); ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- ÅF Tab -->
                                    <?php if (!empty(get_field('af_info'))) : ?>
                                        <div class="tab-pane" id="af">
                                            <?php echo wp_kses_post(get_field('af_info')); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <?= _sc('button', [
                                    'text' => 'Jav vill ha offert   ',
                                    'type' => 'primary',
                                    'href' => '#inquiry-form-wrapper-services'
                                ]); ?>
                            </div>
                        </div>

                    </div>
                </div>

            </section>

    <?php endwhile;
    endif; ?>


    <?php
    $selected_staff_index = get_field('selected_staff');
    $staff_member = array();
    if ($selected_staff_index !== null && $selected_staff_index !== '') {
        $staff_list = get_field('staff_list', 'option');

        if ($staff_list && isset($staff_list[$selected_staff_index])) {
            $staff_member = $staff_list[$selected_staff_index];
        }
    }
    ?>

    <!-- CTA Section -->
    <section class="<?= $prefix; ?>-cta-section">
        <div class="<?= $prefix; ?>-cta-section-inner container">
            <div class="<?= $prefix; ?>-staff-member">

                <h2 class="<?= $prefix; ?>-cta-title"> Vill du veta mer? </h2>
                <p class="<?= $prefix; ?>-cta-text">Vi finns här med personlig service och mångårig erfarenhet, kontakta oss idag så hjälper vi dig vidare med din frågeställning. Fyll i formuläret eller ring oss.</p>


                <?php if (!empty($staff_member)) : ?>
                    <div class="<?= $prefix; ?>-staff-wrapper">
                        <img class="<?= $prefix; ?>-staff-image" srcset="<?php echo wp_get_attachment_image_srcset($staff_member['image']['id'], 'full'); ?>" alt="<?php echo esc_attr($staff_member['name']); ?>" />
                        <div class="<?= $prefix; ?>-staff-content">
                            <h4 class="<?= $prefix; ?>-staff-name"><?= $staff_member['name']; ?></h4>
                            <a class="<?= $prefix; ?>-staff-phone" href="tel:<?= $staff_member['phone']; ?>"> <?= $staff_member['phone']; ?></a>
                        </div>
                    </div>
                <?php else : ?>
                    <?= _sc('button', [
                        'href' => '/kontakt',
                        'text' => 'Kontakta oss',
                        'type' => 'primary',
                    ]); ?>
                <?php endif; ?>


            </div>

            <?= _sc('contact-form', [
                'id' => 'b041fb0',
                'anchor' => 'services'
            ]); ?>
        </div>
    </section>

</main>

<?php get_footer(); ?>