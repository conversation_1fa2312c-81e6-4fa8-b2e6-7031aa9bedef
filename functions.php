<?php
// functions.php

// Register navigation menus
function sharper_register_nav_menus()
{
	register_nav_menus(array(
		'header-menu' => __('Header Menu', 'sharper'),
		'mobile-menu' => __('Mobile Menu', 'sharper'),
		'main-menu' => __('Main Menu', 'sharper'),
	));
}
add_action('after_setup_theme', 'sharper_register_nav_menus');

if (!class_exists('Custom_Walker_Nav_Menu')) {
	class Custom_Walker_Nav_Menu extends Walker_Nav_Menu
	{
		public function start_lvl(&$output, $depth = 0, $args = null)
		{
			$output .= '<ul class="sub-menu">';
		}

		public function end_lvl(&$output, $depth = 0, $args = null)
		{
			$output .= '</ul>';
		}

		public function start_el(&$output, $item, $depth = 0, $args = null, $id = 0)
		{
			$classes = empty($item->classes) ? array() : (array) $item->classes;
			$class_names = join(' ', $classes);

			$output .= '<li class="' . esc_attr($class_names) . '">';

			$output .= '<a href="' . esc_url($item->url) . '">';
			$output .= esc_html($item->title);
			$output .= '</a>';
		}

		public function end_el(&$output, $item, $depth = 0, $args = null)
		{
			$output .= '</li>';
		}
	}
}

function enqueue_custom_styles_scripts()
{
	wp_enqueue_style('child-theme-css', get_stylesheet_directory_uri() . '/style.css', array(), '1.0', 'all');
	wp_enqueue_style('header-theme-css', get_stylesheet_directory_uri() . '/header.css', array(), '1.0', 'all');
	wp_enqueue_style('footer-theme-css', get_stylesheet_directory_uri() . '/footer.css', array(), '1.0', 'all');
	wp_enqueue_style('single-product-css', get_stylesheet_directory_uri() . '/single-product.css', array(), '1.0', 'all');
	wp_enqueue_style('archive-product-css', get_stylesheet_directory_uri() . '/archive-product.css', array(), '1.0', 'all');
	wp_enqueue_style('my-account-css', get_stylesheet_directory_uri() . '/my-account.css', array(), '1.6', 'all');

	// Enqueue single product script only on product pages
	if (is_product()) {
		// Swiper CSS and JS for product gallery
		wp_enqueue_style('swiper-css', 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css', array(), '11.0.0');
		wp_enqueue_script('swiper-js', 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js', array(), '11.0.0', true);


		// Check if this is an education product and enqueue education modal script
		global $product;
		if ($product && is_object($product) && method_exists($product, 'get_id')) {
			$product_cats = wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'slugs'));
			$is_education = in_array('utbildningar', $product_cats);

			if ($is_education) {
				wp_enqueue_script('education-modal-js', get_stylesheet_directory_uri() . '/js/education-modal.js', array('jquery'), '1.0', true);
			}
		}
	}
}
add_action('wp_enqueue_scripts', 'enqueue_custom_styles_scripts');

/* Register blocks */
function register_acf_block_types()
{
	acf_register_block_type(array(
		'name' => 'fifty-fifty-callouts',
		'title' => __("Smort - Fifty Fifty Callouts"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/fifty-fifty-callouts/fifty-fifty-callouts.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/fifty-fifty-callouts/fifty-fifty-callouts.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/fifty-fifty-callouts/fifty-fifty-callouts.js'
	));
	acf_register_block_type(array(
		'name' => 'cta-section',
		'title' => __("Smort - Cta Section"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/cta-section/cta-section.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/cta-section/cta-section.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/cta-section/cta-section.js'
	));
	acf_register_block_type(array(
		'name' => 'cta-grid',
		'title' => __("Smort - CTA Grid"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/cta-grid/cta-grid.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/cta-grid/cta-grid.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/cta-grid/cta-grid.js'
	));


	acf_register_block_type(array(
		'name'              => 'hero',
		'title'             => __('Smort - Hero'),
		'description'       => __('Smort block'),
		'render_template'   => '/template-parts/smortblocks/hero/hero.php',
		'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/hero/hero.css',
		'category' => 'smort',
		'icon' => 'id',
		'mode' => 'edit',
		'keywords'          => array('hero', 'banner', 'header'),
	));

	acf_register_block_type(array(
		'name'              => 'about',
		'title'             => __('Smort - About'),
		'description'       => __('Smort block'),
		'render_template'   => '/template-parts/smortblocks/about/about.php',
		'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/about/about.css',
		'category'          => 'formatting',
		'icon'              => 'groups',
		'keywords'          => array('about', 'om oss', 'text'),
		'supports'          => array(
			'align' => array('wide', 'full'),
			'anchor' => true,
		),
	));
}



// Check if function exists and hook into setup.
if (function_exists('acf_register_block_type')) {
	add_action('acf/init', 'register_acf_block_types');
}

/* Include component functions */
require_once get_stylesheet_directory() . '/components/index.php';

/* Include ACF field groups */
require_once get_stylesheet_directory() . '/template-parts/smortblocks/fifty-fifty-callouts/acf/fifty-fifty-callouts.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/cta-section/acf/cta-section.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/cta-grid/acf/cta-grid.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/hero/acf/hero.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/about/acf/about.php';



/* Add the excerpt field to the product edit page */
function add_excerpt_support()
{
	// Enable excerpts for posts
	add_post_type_support('post', 'excerpt');

	// Optional: Enable excerpts for pages
	add_post_type_support('page', 'excerpt');
}
add_action('init', 'add_excerpt_support');


//remove admin panel on top
function remove_admin_login_header()
{
	remove_action('wp_head', '_admin_bar_bump_cb');
}
add_action('get_header', 'remove_admin_login_header');
