<?php
// functions.php

// Register navigation menus
function sharper_register_nav_menus()
{
	register_nav_menus(array(
		'header-menu' => __('Header Menu', 'smort'),
		'mobile-menu' => __('Mobile Menu', 'smort'),
		'main-menu' => __('Main Menu', 'smort'),
		'footer-menu' => __('Footer Menu', 'smort'),
	));
}
add_action('after_setup_theme', 'sharper_register_nav_menus');

if (!class_exists('Custom_Walker_Nav_Menu')) {
	class Custom_Walker_Nav_Menu extends Walker_Nav_Menu
	{
		public function start_lvl(&$output, $depth = 0, $args = null)
		{
			$output .= '<ul class="sub-menu">';
		}

		public function end_lvl(&$output, $depth = 0, $args = null)
		{
			$output .= '</ul>';
		}

		public function start_el(&$output, $item, $depth = 0, $args = null, $id = 0)
		{
			$classes = empty($item->classes) ? array() : (array) $item->classes;
			$class_names = join(' ', $classes);

			$output .= '<li class="' . esc_attr($class_names) . '">';

			$output .= '<a href="' . esc_url($item->url) . '">';
			$output .= esc_html($item->title);
			$output .= '</a>';
		}

		public function end_el(&$output, $item, $depth = 0, $args = null)
		{
			$output .= '</li>';
		}
	}
}

function enqueue_custom_styles_scripts()
{
	wp_enqueue_style('child-theme-css', get_stylesheet_directory_uri() . '/style.css', array(), '1.0', 'all');
	wp_enqueue_style('header-theme-css', get_stylesheet_directory_uri() . '/header.css', array(), '1.0', 'all');
	wp_enqueue_style('footer-theme-css', get_stylesheet_directory_uri() . '/footer.css', array(), '1.0', 'all');

	if (is_singular('services')) {
		wp_enqueue_style('single-services-css', get_stylesheet_directory_uri() . '/single-services.css', array(), '1.0', 'all');
	} elseif (is_page_template('page-services.php')) {
		wp_enqueue_style('services-page-css', get_stylesheet_directory_uri() . '/page-services.css', array(), '1.0', 'all');
	}

	// Fancybox for lightbox functionality
	wp_enqueue_style('fancybox-css', 'https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css', array(), '5.0.0');
	wp_enqueue_script('fancybox-js', 'https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js', array(), '5.0.0', true);


	wp_enqueue_script('single-services-js', get_stylesheet_directory_uri() . '/js/single-services.js', array('jquery', 'fancybox-js'), '1.0', true);
}
add_action('wp_enqueue_scripts', 'enqueue_custom_styles_scripts');

/* Register blocks */
function register_acf_block_types()
{
	acf_register_block_type(array(
		'name' => 'fifty-fifty-callouts',
		'title' => __("Smort - Fifty Fifty Callouts"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/fifty-fifty-callouts/fifty-fifty-callouts.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/fifty-fifty-callouts/fifty-fifty-callouts.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/fifty-fifty-callouts/fifty-fifty-callouts.js'
	));
	acf_register_block_type(array(
		'name' => 'cta-section',
		'title' => __("Smort - Cta Section"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/cta-section/cta-section.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/cta-section/cta-section.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/cta-section/cta-section.js'
	));
	acf_register_block_type(array(
		'name' => 'cta-grid',
		'title' => __("Smort - CTA Grid"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/cta-grid/cta-grid.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/cta-grid/cta-grid.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/cta-grid/cta-grid.js'
	));


	acf_register_block_type(array(
		'name'              => 'hero',
		'title'             => __('Smort - Hero'),
		'description'       => __('Smort block'),
		'render_template'   => '/template-parts/smortblocks/hero/hero.php',
		'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/hero/hero.css',
		'category' => 'smort',
		'icon' => 'id',
		'mode' => 'edit',
		'keywords'          => array('hero', 'banner', 'header'),
	));

	acf_register_block_type(array(
		'name'              => 'about',
		'title'             => __('Smort - About'),
		'description'       => __('Smort block'),
		'render_template'   => '/template-parts/smortblocks/about/about.php',
		'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/about/about.css',
		'category'          => 'formatting',
		'icon'              => 'groups',
		'keywords'          => array('about', 'om oss', 'text'),
		'supports'          => array(
			'align' => array('wide', 'full'),
			'anchor' => true,
		),
	));

	acf_register_block_type(array(
		'name' => 'text-image-block',
		'title' => __("Smort - Text Image Block"),
		'description' => __('Text och bild block med fifty-fifty layout'),
		'render_template' => '/template-parts/smortblocks/text-image-block/text-image-block.php',
		'category' => 'smort',
		'icon' => 'align-pull-left',
		'keywords' => array('text', 'image', 'fifty', 'layout'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/text-image-block/text-image-block.css',
	));

	acf_register_block_type(array(
		'name' => 'contact-map-block',
		'title' => __("Smort - Contact Map Block"),
		'description' => __('Kontakt och karta block med fifty-fifty layout'),
		'render_template' => '/template-parts/smortblocks/contact-map-block/contact-map-block.php',
		'category' => 'smort',
		'icon' => 'location-alt',
		'keywords' => array('contact', 'map', 'kontakt', 'karta'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/contact-map-block/contact-map-block.css',
	));

	acf_register_block_type(array(
		'name' => 'text-block',
		'title' => __("Smort - Text Block"),
		'description' => __('Enkelt textblock med rubrik och innehåll'),
		'render_template' => '/template-parts/smortblocks/text-block/text-block.php',
		'category' => 'smort',
		'icon' => 'editor-alignleft',
		'keywords' => array('text', 'content', 'rubrik'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/text-block/text-block.css',
	));

	acf_register_block_type(array(
		'name' => 'logo-grid',
		'title' => __("Smort - Logo Grid"),
		'description' => __('Grid med loggor för partners/kunder'),
		'render_template' => '/template-parts/smortblocks/logo-grid/logo-grid.php',
		'category' => 'smort',
		'icon' => 'grid-view',
		'keywords' => array('logo', 'partner', 'kund', 'grid'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/logo-grid/logo-grid.css',
	));

	// Register Retailers block
	acf_register_block_type(array(
		'name' => 'retailers',
		'title' => __('Smort - Återförsäljare'),
		'description' => __('Ett block för att visa återförsäljare'),
		'render_template' => 'template-parts/smortblocks/retailers/retailers.php',
		'category' => 'smort-blocks',
		'icon' => 'store',
		'keywords' => array('retailers', 'återförsäljare', 'butiker'),
		'supports' => array(
			'align' => array('wide', 'full'),
			'anchor' => true,
			'customClassName' => true,
		),
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/retailers/retailers.css',
	));

	// Register Staff block
	acf_register_block_type(array(
		'name' => 'staff',
		'title' => __('Smort -Personal'),
		'description' => __('Ett block för att visa personal'),
		'render_template' => 'template-parts/smortblocks/staff/staff.php',
		'category' => 'smort-blocks',
		'icon' => 'groups',
		'keywords' => array('staff', 'personal', 'team', 'medarbetare'),
		'supports' => array(
			'align' => array('wide', 'full'),
			'anchor' => true,
			'customClassName' => true,
		),
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/staff/staff.css',
	));
}

// Ensure theme supports are properly set
function setup_theme_supports()
{
	// Add post thumbnail support
	add_theme_support('post-thumbnails');

	// Add support for all post types
	add_post_type_support('page', 'thumbnail');
	add_post_type_support('post', 'thumbnail');
	add_post_type_support('services', 'thumbnail');

	// Add other theme supports
	add_theme_support('title-tag');
	add_theme_support('custom-logo');
}
add_action('after_setup_theme', 'setup_theme_supports', 1);

// Check if function exists and hook into setup.
if (function_exists('acf_register_block_type')) {
	add_action('acf/init', 'register_acf_block_types');
}

/* Include component functions */
require_once get_stylesheet_directory() . '/components/index.php';

/* Include ACF field groups */
require_once get_stylesheet_directory() . '/template-parts/smortblocks/fifty-fifty-callouts/acf/fifty-fifty-callouts.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/cta-section/acf/cta-section.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/cta-grid/acf/cta-grid.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/hero/acf/hero.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/about/acf/about.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/text-block/acf/text-block.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/logo-grid/acf/logo-grid.php';

// Include Tjänster ACF fields
require_once get_stylesheet_directory() . '/template-parts/smortblocks/services/acf/page-services.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/services/acf/single-services.php';

// Include text image block ACF fields
require_once get_stylesheet_directory() . '/template-parts/smortblocks/text-image-block/acf/text-image-block.php';

// Include contact map block ACF fields
require_once get_stylesheet_directory() . '/template-parts/smortblocks/contact-map-block/acf/contact-map-block.php';

// Include logo grid ACF fields
require_once get_stylesheet_directory() . '/template-parts/smortblocks/logo-grid/acf/logo-grid.php';

// Include Retailers ACF fields
require_once get_stylesheet_directory() . '/template-parts/smortblocks/retailers/acf/retailers-settings.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/retailers/acf/retailers.php';

// Include Staff ACF fields
require_once get_stylesheet_directory() . '/template-parts/smortblocks/staff/acf/staff.php';
require_once get_stylesheet_directory() . '/template-parts/smortblocks/staff/acf/staff-settings.php';

function program_post_type()
{

	register_post_type(
		'services',
		array(
			'labels' => array(
				'name' => __('Tjänster'),
				'singular_name' => __('Tjänst')
			),
			'public' => true,
			'show_in_rest' => true,
			'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
			'menu_icon' => 'dashicons-hammer',
			'has_archive' => true,
			'rewrite' => array('slug' => 'services'),
			'taxonomies' => array('category', 'post_tag'),
		)
	);
}
add_action('init', 'program_post_type');

function services_category_taxonomy()
{
	register_taxonomy(
		'services_category',
		'services',
		array(
			'labels' => array(
				'name' => __('Tjänstekategorier'),
				'singular_name' => __('Tjänstekategori')
			),
			'hierarchical' => true,
			'show_in_rest' => true,
			'show_ui' => true,
			'show_admin_column' => true,
			'query_var' => true,
			'rewrite' => array('slug' => 'services-category'),
			'public' => true,
		)
	);
}
add_action('init', 'services_category_taxonomy');

//remove admin top bar for everyone , also for admin
add_action('after_setup_theme', function () {
	if (is_admin_bar_showing()) {
		show_admin_bar(false);
	}
});


function smort_child_enqueue_retailers_styles()
{
	if (is_singular('services')) {
		wp_enqueue_style(
			'smort-retailers',
			get_stylesheet_directory_uri() . '/template-parts/smortblocks/retailers/retailers.css',
			[],
			filemtime(get_stylesheet_directory() . '/template-parts/smortblocks/retailers/retailers.css')
		);
	}
}
add_action('wp_enqueue_scripts', 'smort_child_enqueue_retailers_styles');
