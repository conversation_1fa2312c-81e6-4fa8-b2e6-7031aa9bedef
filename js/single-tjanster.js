jQuery(document).ready(function ($) {
  // Initialize Fancybox for lightbox effect
  if (typeof Fancybox !== "undefined") {
    Fancybox.bind('[data-fancybox="gallery"]', {
      Toolbar: {
        display: {
          left: ["infobar"],
          middle: [
            "zoomIn",
            "zoomOut",
            "toggle1to1",
            "rotateCCW",
            "rotateCW",
            "flipX",
            "flipY",
          ],
          right: ["slideshow", "thumbs", "close"],
        },
      },
      Thumbs: {
        autoStart: true,
      },
      Images: {
        zoom: true,
      },
      Carousel: {
        infinite: true,
      },
    });
  }

  // Tab functionality
  $(".tab-btn").on("click", function () {
    const tabId = $(this).data("tab");

    // Remove active class from all tabs and panes
    $(".tab-btn").removeClass("active");
    $(".tab-pane").removeClass("active");

    // Add active class to clicked tab and corresponding pane
    $(this).addClass("active");
    $("#" + tabId).addClass("active");
  });

  // Thumbnail functionality
  $(".thumbnail").on("click", function () {
    const index = $(this).data("index");

    // Remove active class from all thumbnails and images
    $(".thumbnail").removeClass("active");
    $(".product-image").removeClass("active");

    // Add active class to clicked thumbnail and corresponding image
    $(this).addClass("active");
    $(".product-image").eq(index).addClass("active");
  });

  // Modal functionality
  $("#product-inquiry-btn").on("click", function () {
    $("#inquiry-modal").addClass("active");
    $("body").addClass("modal-open");
  });

  $("#modal-close, .modal-overlay").on("click", function () {
    $("#inquiry-modal").removeClass("active");
    $("body").removeClass("modal-open");
  });

  // Prevent modal close when clicking inside modal content
  $(".modal-content").on("click", function (e) {
    e.stopPropagation();
  });

  // Smooth scroll for anchor links
  $('a[href^="#"]').on("click", function (e) {
    e.preventDefault();
    const target = $($(this).attr("href"));
    if (target.length) {
      $("html, body").animate(
        {
          scrollTop: target.offset().top - 100,
        },
        500
      );
    }
  });
});

// Add body class to prevent scrolling when modal is open
document.addEventListener("DOMContentLoaded", function () {
  const style = document.createElement("style");
  style.textContent = `
        body.modal-open {
            overflow: hidden;
        }
    `;
  document.head.appendChild(style);
});
