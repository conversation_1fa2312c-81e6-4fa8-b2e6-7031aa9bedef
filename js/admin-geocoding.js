jQuery(document).ready(function ($) {
  let geocoder;
  let geocodingTimeout;

  // Initiera geocoder när Google Maps API är laddat
  function initGeocoder() {
    if (typeof google !== "undefined" && google.maps && google.maps.Geocoder) {
      geocoder = new google.maps.Geocoder();
      setupAddressListeners();
    } else {
      // Försök igen om 500ms
      setTimeout(initGeocoder, 500);
    }
  }

  function setupAddressListeners() {
    // Lyssna på ändringar i adressfält
    $(document).on("input", 'input[name*="[address]"]', function () {
      const $addressField = $(this);
      const address = $addressField.val().trim();

      if (address.length < 5) return; // För kort adress

      // Hitta motsvarande lat/lng fält
      const fieldName = $addressField.attr("name");
      const latFieldName = fieldName.replace("[address]", "[latitude]");
      const lngFieldName = fieldName.replace("[address]", "[longitude]");

      const $latField = $('input[name="' + latFieldName + '"]');
      const $lngField = $('input[name="' + lngFieldName + '"]');

      // Debounce geocoding requests
      clearTimeout(geocodingTimeout);
      geocodingTimeout = setTimeout(function () {
        geocodeAddress(address, $latField, $lngField, $addressField);
      }, 1000);
    });
  }

  function geocodeAddress(address, $latField, $lngField, $addressField) {
    if (!geocoder) return;

    // Lägg till Sverige som standard om inte specificerat
    let searchAddress = address;
    if (
      !address.toLowerCase().includes("sverige") &&
      !address.toLowerCase().includes("sweden")
    ) {
      searchAddress += ", Sverige";
    }

    // Visa loading state
    $addressField.css("border-color", "#ffa500");

    geocoder.geocode(
      {
        address: searchAddress,
        region: "SE", // Prioritera svenska resultat
      },
      function (results, status) {
        if (status === "OK" && results[0]) {
          const location = results[0].geometry.location;
          const lat = location.lat();
          const lng = location.lng();

          // Uppdatera fälten
          $latField.val(lat.toFixed(6));
          $lngField.val(lng.toFixed(6));

          // Visa success state
          $addressField.css("border-color", "#00a32a");

          // Återställ border efter 2 sekunder
          setTimeout(function () {
            $addressField.css("border-color", "");
          }, 2000);

          console.log("Geocoding successful:", address, "→", lat, lng);
        } else {
          // Visa error state
          $addressField.css("border-color", "#d63638");
          console.warn("Geocoding failed:", status, "for address:", address);

          // Återställ border efter 3 sekunder
          setTimeout(function () {
            $addressField.css("border-color", "");
          }, 3000);
        }
      }
    );
  }

  // Starta geocoder initialization
  initGeocoder();
});
