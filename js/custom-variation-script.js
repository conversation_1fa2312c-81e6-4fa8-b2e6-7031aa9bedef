jQuery(document).ready(function($) {
    $('.variation-button').on('click', function() {
        var attribute_name = $(this).data('attribute_name');
        var attribute_value = $(this).data('attribute_value');

        $(this).siblings('.variation-button').removeClass('selected');
        $(this).addClass('selected');

        $('select[name="' + attribute_name + '"]').val(attribute_value).trigger('change');
    });
});
