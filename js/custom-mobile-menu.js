// Mobile Menu Script
document.addEventListener('DOMContentLoaded', function() {
    const burgerMenu = document.getElementById('burger-menu');
    const mobileNav = document.getElementById('mobile-nav');
    const closeNav = document.getElementById('close-nav');

    burgerMenu.addEventListener('click', function() {
        mobileNav.classList.toggle('active');
    });

    closeNav.addEventListener('click', function() {
        mobileNav.classList.remove('active');
    });

    document.querySelectorAll('.mobile-nav ul li.menu-item-has-children > a').forEach(item => {
        item.addEventListener('click', function(event) {
            event.preventDefault();
            const parent = item.parentElement;
            parent.classList.toggle('active');
        });
    });
});
</script>