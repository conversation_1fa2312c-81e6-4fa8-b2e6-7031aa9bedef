document.addEventListener('DOMContentLoaded', function() {
    const header = document.getElementById('main-header');
    const triggers = header.querySelectorAll('.menu-item-has-children > a');
    const submenues = header.querySelectorAll('.menu-item-has-children > ul');

    const handleToggleSubmenu = (id) => {
      const submenu = header.querySelector(`#${id}`);
      const trigger = header.querySelector(`[aria-controls="${id}"]`);
    
      // Close all other submenus first
      submenues.forEach(sub => {
        if (sub.id !== id) {
          sub.setAttribute('aria-hidden', 'true');
          sub.parentElement.classList.remove('active');
          const otherTrigger = header.querySelector(`[aria-controls="${sub.id}"]`);
          otherTrigger.setAttribute('aria-expanded', 'false');
          sub.classList.remove('active');
        }
      });

      // Toggle the target submenu
      const isExpanded = trigger.getAttribute('aria-expanded') === 'true';
      trigger.setAttribute('aria-expanded', !isExpanded);
      submenu.setAttribute('aria-hidden', isExpanded);
      submenu.parentElement.classList.toggle('active');
    }

    submenues.forEach((submenu, index) => {
      submenu.setAttribute('id', `submenu-${index}`);
      submenu.setAttribute('aria-hidden', 'true');
      submenu.setAttribute('role', 'menu');
    });
    triggers.forEach((trigger, index) => {
      trigger.setAttribute('aria-expanded', 'false');
      trigger.setAttribute('aria-haspopup', 'true');
      trigger.setAttribute('aria-controls', `submenu-${index}`);
      trigger.setAttribute('aria-label', 'Press space to open submenu');
      
      trigger.addEventListener('keydown', function(e) {
          if (e.code === 'Space' ) {
            e.preventDefault(); // Prevent default space behavior
            e.stopPropagation(); // Stop event bubbling
            handleToggleSubmenu(`submenu-${index}`);
          }
      });
    });


});


