jQuery(document).ready(function($) {
    // Ensure decimals are handled correctly
    Number.prototype.getDecimals = function() {
        var match = ('' + this).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);
        if (!match) { return 0; }
        return Math.max(
            0,
            (match[1] ? match[1].length : 0) - (match[2] ? +match[2] : 0)
        );
    };

    // Attach click event listeners to the plus and minus buttons
    $('form.cart, .woocommerce-cart-form').on('click', '.plus, .minus', function(e) {
        e.preventDefault(); // Prevent default action

        // Get the current quantity input element
        var $qty = $(this).closest('.quantity').find('.qty'),
            currentVal = parseFloat($qty.val()),
            max = parseFloat($qty.attr('max')),
            min = parseFloat($qty.attr('min')),
            step = parseFloat($qty.attr('step'));

        // Ensure current value is a number
        if (isNaN(currentVal) || currentVal === '') currentVal = 0;
        if (isNaN(max)) max = '';
        if (isNaN(min)) min = 0;
        if (isNaN(step) || step === 'any' || step === '') step = 1;

        // Adjust the value
        if ($(this).hasClass('plus')) {
            if (max !== '' && currentVal >= max) {
                $qty.val(max);
            } else {
                $qty.val((currentVal + step).toFixed(step.getDecimals()));
            }
        } else {
            if (min !== '' && currentVal <= min) {
                $qty.val(min);
            } else if (currentVal > 0) {
                $qty.val((currentVal - step).toFixed(step.getDecimals()));
            }
        }

        // Trigger change event
        $qty.trigger('change');
    });
});
