document.addEventListener('DOMContentLoaded', function() {
    const slider = document.querySelector('.services-services-swiper');
    let isDown = false;
    let startX;
    let scrollLeft;
    console.log('slider in file');
    slider.addEventListener('mousedown', (e) => {
        isDown = true;
        startX = e.pageX - slider.offsetLeft;
        scrollLeft = slider.scrollLeft;

        console.log('🟡 ~ isDown',isDown);
        console.log('🟡 ~ scrollLeft',scrollLeft);
    });

    slider.addEventListener('mouseleave', () => {
        isDown = false;
    });

    slider.addEventListener('mouseup', () => {
        isDown = false;
        slider.classList.remove('active');
    });

    slider.addEventListener('mousemove', (e) => {
        if (!isDown) return;
        e.preventDefault();
        const x = e.pageX - slider.offsetLeft;
        const walk = (x - startX) * 2;
        slider.scrollLeft = scrollLeft - walk;
    });
});
