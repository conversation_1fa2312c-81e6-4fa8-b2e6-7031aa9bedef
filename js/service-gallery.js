    // BILDGALLERI PRODUKTBILDER 
    document.addEventListener('DOMContentLoaded', function() {
        const thumbnails = document.querySelectorAll('.thumbnail');
        const mainImage = document.getElementById('main-image');
        const mainImageLink = document.getElementById('main-image-link');
        const prevBtn = document.querySelector('.slider-arrow.prev');
        const nextBtn = document.querySelector('.slider-arrow.next');
        const thumbnailWrapper = document.querySelector('.thumbnail-wrapper');
        const mainImageContainer = document.querySelector('.main-image-container');
        let currentIndex = 0;
        let thumbnailIndex = 0;

        // Touch/swipe variables
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let isDragging = false;

        function updateMainImage(index) {
            const newSrc = thumbnails[index].getAttribute('data-full');
            mainImage.setAttribute('src', newSrc);
            mainImageLink.setAttribute('href', newSrc);
            thumbnails.forEach(t => t.classList.remove('active'));
            thumbnails[index].classList.add('active');
            currentIndex = index;
        }

        function scrollToThumbnail(index) {
            if (thumbnails.length === 1) return;

            const thumbnail = thumbnails[index];
            const width = thumbnail.offsetWidth;
            if (index === 0) {
                thumbnailWrapper.scrollLeft = 0;
            } else if (index > thumbnailIndex) {
                thumbnailWrapper.scrollLeft += (width + 10)
            } else {
                thumbnailWrapper.scrollLeft -= (width + 10);
            }
            thumbnailIndex = index;
        }

        function goToNextImage() {
            let newIndex = (currentIndex + 1) % thumbnails.length;
            updateMainImage(newIndex);
            scrollToThumbnail(newIndex);
        }

        function goToPrevImage() {
            let newIndex = (currentIndex - 1 + thumbnails.length) % thumbnails.length;
            updateMainImage(newIndex);
            scrollToThumbnail(newIndex);
        }

        // Touch events for swipe
        mainImageContainer.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            isDragging = true;
            mainImageContainer.classList.add('swiping');
        });

        mainImageContainer.addEventListener('touchmove', (e) => {
            if (!isDragging) return;

            const currentX = e.touches[0].clientX;
            const currentY = e.touches[0].clientY;
            const diffX = Math.abs(startX - currentX);
            const diffY = Math.abs(startY - currentY);

            // Bara förhindra default om det är mer horisontell än vertikal rörelse
            if (diffX > diffY && diffX > 10) {
                e.preventDefault();

                const diff = startX - currentX;
                if (diff > 0) {
                    mainImageContainer.classList.add('swipe-left');
                    mainImageContainer.classList.remove('swipe-right');
                } else {
                    mainImageContainer.classList.add('swipe-right');
                    mainImageContainer.classList.remove('swipe-left');
                }
            }
        });

        mainImageContainer.addEventListener('touchend', (e) => {
            if (!isDragging) return;
            endX = e.changedTouches[0].clientX;
            isDragging = false;

            const swipeThreshold = 50;
            const swipeDistance = startX - endX;

            if (Math.abs(swipeDistance) > swipeThreshold) {
                if (swipeDistance > 0) {
                    // Swipe left - next image
                    goToNextImage();
                } else {
                    // Swipe right - previous image
                    goToPrevImage();
                }
            }
        });

        // Mouse events for desktop drag
        mainImageContainer.addEventListener('mousedown', (e) => {
            startX = e.clientX;
            isDragging = true;
            e.preventDefault();
        });

        mainImageContainer.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            e.preventDefault();
        });

        mainImageContainer.addEventListener('mouseup', (e) => {
            if (!isDragging) return;
            endX = e.clientX;
            isDragging = false;

            const swipeThreshold = 50;
            const swipeDistance = startX - endX;

            if (Math.abs(swipeDistance) > swipeThreshold) {
                if (swipeDistance > 0) {
                    goToNextImage();
                } else {
                    goToPrevImage();
                }
            }
        });

        thumbnails.forEach((thumb, index) => {
            thumb.addEventListener('click', () => {
                updateMainImage(index);
                scrollToThumbnail(index);
            });
        });

        prevBtn.addEventListener('click', goToPrevImage);
        nextBtn.addEventListener('click', goToNextImage);

        // Initiera
        updateMainImage(currentIndex);
    });

