// Fancybox 4 init for gallery
jQuery(document).ready(function ($) {
  if ($('[data-fancybox="gallery"]').length && window.Fancybox) {
    Fancybox.bind('[data-fancybox="gallery"]', {
      Thumbs: {
        autoStart: true,
      },
      Toolbar: {
        display: [
          { id: "counter", position: "center" },
          "zoom",
          "slideshow",
          "fullscreen",
          "download",
          "thumbs",
          "close",
        ],
      },
      compact: false,
      dragToClose: true,
      animated: true,
      closeButton: "top",
      showClass: "fancybox-zoomIn",
      hideClass: "fancybox-zoomOut",
    });
  }

  // Swiper init for product slider with thumbs
  if ($(".product-swiper-main").length && $(".product-swiper-thumbs").length) {
    var thumbsSwiper = new Swiper(".product-swiper-thumbs", {
      slidesPerView: "auto",
      spaceBetween: 8,
      watchSlidesProgress: true,
      freeMode: true,
    });
    new Swiper(".product-swiper-main", {
      loop: true,
      slidesPerView: 1,
      spaceBetween: 0,
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
      thumbs: {
        swiper: thumbsSwiper,
      },
      effect: "fade",
      fadeEffect: { crossFade: true },
      speed: 600,
      grabCursor: true,
      simulateTouch: true,
    });
  } else if ($(".product-swiper-main").length) {
    new Swiper(".product-swiper-main", {
      loop: true,
      slidesPerView: 1,
      spaceBetween: 0,
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
      effect: "fade",
      fadeEffect: { crossFade: true },
      speed: 600,
      grabCursor: true,
      simulateTouch: true,
    });
  }
});
