document.addEventListener('DOMContentLoaded', function() {
    const header = document.getElementById('main-header');
    const headerContainer = header.querySelector('.header-container');
    const menuContainer = header.querySelector('.menu-container');
    const logo = header.querySelector('.smort-logo');
    let isMobileLayout;
    
    function checkHeaderWidth() {
        // If we're in mobile layout, temporarily remove it to measure true widths
        const wasMobile = header.classList.contains('force-mobile-layout');
        if (wasMobile) {
            header.classList.remove('force-mobile-layout');
            // Give the DOM time to update
            setTimeout(() => {
                measureAndAdjust();
            }, 10);
        } else {
            measureAndAdjust();
        }
    }

    function measureAndAdjust() {
        // Reset any temporary styles
        headerContainer.style.width = '';
        menuContainer.style.display = '';
        
        // Get the container width and the sum of all child elements
        const containerWidth = headerContainer.offsetWidth;
        const logoWidth = logo.offsetWidth;
        const menuWidth = menuContainer.offsetWidth;
        const safetyMargin = 40;
        const totalContentWidth = logoWidth + menuWidth + safetyMargin;

        
        if (totalContentWidth > containerWidth || totalContentWidth > window.innerWidth) {
            if (!header.classList.contains('force-mobile-layout')) {
                header.classList.add('force-mobile-layout');
                isMobileLayout = true;
            }
        } else {
            if (containerWidth - totalContentWidth > safetyMargin && header.classList.contains('force-mobile-layout')) {
                header.classList.remove('force-mobile-layout');
                isMobileLayout = false;
            }
        }

        // Handle submenu links
        const submenuLinks = document.querySelectorAll('.mobile-menu li.menu-item-has-children > a');
        submenuLinks.forEach(function(link) {
            const newLink = link.cloneNode(true);
            link.parentNode.replaceChild(newLink, link);
            
            newLink.addEventListener('click', function(e) {
                if (isMobileLayout) {
                    e.preventDefault();
                    this.parentElement.classList.toggle('active');
                }
            });
        });
    }
    
    // Run on load
    checkHeaderWidth();

    // Run on resize with debounce
    let resizeTimer;
    const handleResize = () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(checkHeaderWidth, 250);
    };

    window.addEventListener('resize', handleResize);
});
