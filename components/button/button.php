<?php

/**
 * Button Component
 * 
 * @param string $example Example prop
 * 
 */

// Set default values for props
// $example = $props['key'] ?? 'default';
$href = $props['href'] ?? '';
$text = $props['text'] ?? '<PERSON><PERSON><PERSON> mer';
$target = $props['target'] ?? '_self';
$type = $props['type'] ?? 'primary';
$size = $props['size'] ?? 'medium';
$disabled = $props['disabled'] ?? false;

// Build CSS classes
$prefix = 'button';
$css_classes = [$prefix, $prefix . '-' . $type, $prefix . '-' . $size];
// Alternative CSS Class building
//$css_classes = ['button', 'button-' . $type, 'button-' . $size];

// Get button.css styling
wp_enqueue_style('button-style', get_stylesheet_directory_uri() . '/components/button/button.css', array(), '1.0', 'all');

if (!empty($class)) {
  $css_classes[] = $class;
}

$class_string = implode(' ', $css_classes);

// Build attributes
$attributes = [];

if (!empty($id)) {
  $attributes[] = "id=\"{$id}\"";
}

if (!empty($onclick)) {
  $attributes[] = "onclick=\"{$onclick}\"";
}

if ($disabled) {
  $attributes[] = 'disabled';
}

$attributes_string = implode(' ', $attributes);

// Render the component
?>

<?php if (!empty($href)) : ?>
  <a href="<?php echo esc_url($href); ?>" target="<?= $target ?>" class="<?php echo esc_attr($class_string); ?>" <?php echo $attributes_string; ?>>
    <span>
      <?= $text ?>
    </span>

    <div class="<?= $prefix ?>-arrow-container">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#1e4d2d" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right">
        <line x1="5" y1="12" x2="19" y2="12"></line>
        <polyline points="12 5 19 12 12 19"></polyline>
      </svg>
    </div>
  </a>
<?php else : ?>
  <button class="<?php echo esc_attr($class_string); ?>" <?php echo $attributes_string; ?>>
    <span>
      <?= $text ?>
    </span>

    <div class="<?= $prefix ?>-arrow-container">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#1e4d2d" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right">
        <line x1="5" y1="12" x2="19" y2="12"></line>
        <polyline points="12 5 19 12 12 19"></polyline>
      </svg>
    </div>
  </button>
<?php endif; ?>