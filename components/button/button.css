/**
 * Button Block Styles
 */

 /* Main Button container */
.button {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width:fit-content;
  gap: var(--spacing-lg);
  cursor:pointer;
  border-radius: var(--border-radius-medium);
  font-family: var(--font-body);
  transition: opacity 0.25s ease-in-out;

  .button-arrow-container {
    border-radius: var(--border-radius-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xs);

    .button-arrow {
      height: 20px;
      width: 20px;
      color: var(--primary-color);
    }
  }
  
  &:hover {
    opacity: 0.9
  }

}

/* Button Prop styles */
.button-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
  border: 1px solid var(--primary-color);
  
  &:hover {
    color: var(--text-white);
  }
  .button-arrow-container{
    background-color: var(--accent-color);
  }
  
}

.button-accent {
  background-color: var(--accent-color);
  border: 1px solid var(--accent-color);
  color: var(--text-black);

  &:hover {
    color: var(--text-black);
  }
  
  .button-arrow-container{
    background-color: var(--white);
  }
}

.button-white {
  background-color: var(--text-white);
  border: 1px solid var(--black);
  color: var(--text-black);
  
  &:hover {
    color: var(--text-black);
  }

  .button-arrow-container{
    background-color: var(--accent-color);
  }
}

.button-small {
  /* Add your styles here */
}

.button-medium {
  /* Add your styles here */
  padding: var(--spacing-xs) var(--spacing-sm);
}

.button-large {
  /* Add your styles here */
}

/* Responsive styles */
@media (width <= 1024px) {
  /* Add your styles here */
    .button {
      /* Add your styles here */
    }
}
@media (width <= 768px) {
  /* Add your styles here */
    .button {
      /* Add your styles here */
    }
}
@media (width <= 480px) {
  /* Add your styles here */
    .button {
      /* Add your styles here */
    }
}

