/**
 * Button Block Styles
 */

/* Main Button container */

.button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: fit-content;
  gap: 40px;
  cursor: pointer;
  border-radius: var(--border-radius-medium);
  border-radius: calc(var(--border-radius-small) * 3);
  font-family: var(--font-body);
  transition: opacity 0.25s ease-in-out;
  border: 1px solid var(--primary-color);
  min-width: 250px;
  white-space: nowrap;

  .button-arrow-container {
    border-radius: calc(var(--border-radius-small) * 2);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm);
    .button-arrow {
      height: 20px;
      width: 20px;
      color: var(--primary-color);
    }
  }

  &:hover {
    opacity: 0.9;
  }
}

/* Button Prop styles */
.button-primary {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: var(--text-white);
  border: 1px solid var(--primary-color);

  &:hover {
    color: var(--text-white);
  }
  .button-arrow-container {
    border-color: var(--primary-color);
    background-color: var(--accent-color);
  }
}

.button-accent {
  border-color: var(--accent-color);
  background-color: var(--accent-color);
  border: 1px solid var(--accent-color);
  color: var(--primary-color);

  &:hover {
    color: var(--primary-color);
  }

  .button-arrow-container {
    background-color: var(--white);
  }
}

.button-white {
  border-color: var(--primary-color);
  background-color: var(--text-white);
  border: 1px solid var(--black);
  color: var(--primary-color);

  &:hover {
    color: var(--primary-color);
  }

  .button-arrow-container {
    background-color: var(--accent-color);
  }
}

.button-small {
  /* Add your styles here */
}

.button-medium {
  /* Add your styles here */
  padding: var(--spacing-sm);
  font-size: var(--font-size-small);
}

.button-large {
  /* Add your styles here */
}

/* Responsive styles */
@media (width <= 1024px) {
  /* Add your styles here */
  .button {
    /* Add your styles here */
  }
}
@media (width <= 768px) {
  /* Add your styles here */
  .button {
    /* Add your styles here */
  }
}
@media (width <= 480px) {
  /* Add your styles here */
  .button {
    /* Add your styles here */
  }
}
