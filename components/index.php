<?php

/**
 * Load and render a component with props (React-like functionality)
 *
 * @param string $component_name The name of the component file (without .php extension)
 * @param array $props Props/options to pass to the component
 * @param bool $return Whether to return the output instead of echoing it
 * @return string|void Returns the component output if $return is true
 */
function get_component($component_name, $props = [], $return = false)
{
  $component_file = get_stylesheet_directory() . '/components/' . $component_name . '/' . $component_name . '.php';

  if (!file_exists($component_file)) {
    $error_message = "Component '{$component_name}' not found at: {$component_file}";
    if ($return) {
      return $error_message;
    }
    echo $error_message;
    return;
  }

  // Start output buffering to capture component output
  ob_start();

  // The $props array will be directly available in the component file
  // No need for complex global variables or extraction

  // Include the component file
  include $component_file;

  // Get the buffered content
  $output = ob_get_clean();

  if ($return) {
    return $output;
  }

  echo $output;
}

/**
 * Render a component and return its output as a string
 *
 * @param string $component_name The name of the component
 * @param array $props Props to pass to the component
 * @return string The rendered component output
 */
function render_component($component_name, $props = [])
{
  return get_component($component_name, $props, true);
}
/**
 * Shorthand for get_component()
 * _sc = Smort Component
 * 
 * @param mixed $component_name 
 * @param array $props 
 * @return string|null 
 */
function _sc($component_name, $props = [], $return = false)
{
  return get_component($component_name, $props, $return);
}
