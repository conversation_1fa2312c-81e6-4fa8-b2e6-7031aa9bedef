/**
 * Contact Form Block Styles
 */

/* Contact Form 7 Theme Styling */

.contact-form {
  margin: 0 auto;
  background: var(--accent-color);
  border-radius: var(--border-radius-medium);
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.wpcf7-form {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.wpcf7-form-control-wrap {
  flex: 1 1 100%;
  min-width: 0;
}

.wpcf7 input[type="text"],
.wpcf7 input[type="email"],
.wpcf7 input[type="tel"],
.wpcf7 input[type="date"],
.wpcf7 input[type="time"],
.wpcf7 textarea {
  width: 100%;
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius-medium);
  background: var(--white);
  padding: 1rem;
  font-size: 1rem;
  font-family: var(--font-body);
  color: var(--text-black);
  margin-bottom: 0;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.wpcf7 input:focus,
.wpcf7 textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

.wpcf7 textarea {
  min-height: 120px;
  resize: vertical;
}

.wpcf7 label {
  display: block;
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.wpcf7 input[type="submit"],
.wpcf7 .wpcf7-submit {
  display: inline-block;
  border: none;
  border-radius: var(--border-radius-medium);
  padding: 1rem 2.5rem;
  font-size: 1rem;
  font-family: var(--font-body);
  color: var(--text-white);
  background: var(--primary-color);
  cursor: pointer;
  font-weight: var(--font-weight-bold);
  transition: background 0.2s, box-shadow 0.2s;
  margin-top: 1rem;
}

.wpcf7 input[type="submit"]:hover,
.wpcf7 .wpcf7-submit:hover {
  background: var(--primary-color-dark, #1e4d2d);
}

.wpcf7-response-output,
.wpcf7-not-valid-tip {
  color: var(--error-color, #c00);
  font-size: 0.95rem;
  margin-top: 0.5rem;
}

.wpcf7 .wpcf7-spinner {
  margin-left: 10px;
}

@media (max-width: 768px) {
  .contact-form,
  .wpcf7 {
    padding: 1rem;
  }
  .wpcf7-form {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .contact-form,
  .wpcf7 {
    padding: 0.5rem;
  }
  .wpcf7-form {
    gap: 10px;
  }
  .wpcf7 input[type="submit"],
  .wpcf7 .wpcf7-submit {
    width: 100%;
    padding: 1rem 0;
  }
}

.form-row {
  display: flex;
  gap: 24px;
}

.form-col {
  flex: 1 1 0;
  min-width: 0;
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }
}
