/**
 * Contact Form Block Styles
 */

/* Contact Form 7 Theme Styling */

.contact-form {
  margin: 0 auto;
  background: var(--accent-color);
  border-radius: var(--border-radius-medium);
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  margin-top: -12rem;
}

@media (max-width: 768px) {
  .contact-form {
    margin-top: 0;
  }
}

.wpcf7-form-control-wrap {
  flex: 1 1 100%;
  min-width: 0;
}

.wpcf7 input[type="text"],
.wpcf7 input[type="email"],
.wpcf7 input[type="tel"],
.wpcf7 input[type="date"],
.wpcf7 input[type="time"],
.wpcf7 textarea {
  width: 100%;
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius-medium);
  background: var(--white);
  padding: 1rem;
  font-size: 1rem;
  font-family: var(--font-body);
  color: var(--text-black);
  margin-bottom: 0;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.wpcf7 input:focus,
.wpcf7 textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

.wpcf7 textarea {
  min-height: 120px;
  resize: vertical;
}

.wpcf7 label {
  display: block;
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.wpcf7 input[type="submit"],
.wpcf7 .wpcf7-submit {
  display: inline-block;
  border: none;
  border-radius: var(--border-radius-medium);
  padding: 1rem 2.5rem;
  font-size: 1rem;
  font-family: var(--font-body);
  color: var(--text-white);
  background: var(--primary-color);
  cursor: pointer;
  font-weight: var(--font-weight-bold);
  transition: background 0.2s, box-shadow 0.2s;
  margin-top: 1rem;
}

.wpcf7 input[type="submit"]:hover,
.wpcf7 .wpcf7-submit:hover {
  background: var(--primary-color-dark, #1e4d2d);
}

.wpcf7-response-output,
.wpcf7-not-valid-tip {
  color: var(--error-color, #c00);
  font-size: 0.95rem;
  margin-top: 0.5rem;
}

.wpcf7 .wpcf7-spinner {
  margin-left: 10px;
}

@media (max-width: 768px) {
  .contact-form,
  .wpcf7 {
    padding: 1rem;
  }
  .wpcf7-form {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .contact-form,
  .wpcf7 {
    padding: 0.5rem;
  }
  .wpcf7-form {
    gap: 10px;
  }
  .wpcf7 input[type="submit"],
  .wpcf7 .wpcf7-submit {
    width: 100%;
    padding: 1rem 0;
  }
}

.form-row {
  display: flex;
  gap: 24px;
}

.form-col {
  flex: 1 1 0;
  min-width: 0;
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }
}

.visit-type-section {
  margin-bottom: 1.5rem;
}

.visit-type-label {
  display: block;
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  margin-bottom: 1rem;
  font-size: 1rem;
}

.radio-group {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.wpcf7 .wpcf7-radio {
  display: flex;
  gap: 1.5rem;
  margin: 0;
}

@media (max-width: 768px) {
  .wpcf7 .wpcf7-radio {
    flex-direction: column;
    gap: 1rem;
  }
}

.wpcf7 .wpcf7-list-item {
  margin: 0;
  display: flex;
  align-items: center;
  border-radius: var(--border-radius-medium);
  transition: background-color 0.2s ease;
}

.wpcf7 input[type="radio"] {
  appearance: none;
  width: 32px;
  height: 24px;
  border: 3px solid var(--primary-color);
  border-radius: 50%;
  margin-right: 0.75rem;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--white);
  box-shadow: 0 2px 8px rgba(30, 77, 45, 0.1);
}

.wpcf7 input[type="radio"]:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(30, 77, 45, 0.2);
  transform: scale(1.05);
}

.wpcf7 input[type="radio"]:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 4px 16px rgba(30, 77, 45, 0.3);
}

.wpcf7 input[type="radio"]:checked::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background-color: white;
  border-radius: 50%;
  animation: radioCheck 0.2s ease-in-out;
}

@keyframes radioCheck {
  0% {
    transform: translate(-50%, -50%) scale(0);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}

.wpcf7 .wpcf7-list-item-label {
  color: var(--text-black);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  font-size: 1.1rem;
  margin: 0;
  transition: color 0.2s ease;
  padding: 1rem 1.2rem;
}

@media (max-width: 768px) {
  .radio-group {
    flex-direction: column;
    gap: 1rem;
  }
}

.toggle-wrapper label {
  padding: 1rem 1.2rem;
  background-color: var(--white);
  border-radius: var(--border-radius-medium);
  transition: background-color 0.2s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  border: 1px solid var(--secondary-color);
}

.toggle-wrapper span {
  margin: 0;
}

.wpcf7 .wpcf7-list-item-label::before {
  content: attr(data-title);
  font-weight: var(--font-weight-bold);
  display: block;
  margin-bottom: 2px;
}

/* Alternativ lösning med CSS */
.wpcf7 .wpcf7-list-item:first-child .wpcf7-list-item-label::before {
  content: "Besök Showroom";
}

.wpcf7 .wpcf7-list-item:last-child .wpcf7-list-item-label::before {
  content: "Vi besöker er";
}

.wpcf7 input[type="radio"]:checked + .wpcf7-list-item-label::before {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.wpcf7 input[type="radio"]:checked + .wpcf7-list-item-label {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}
