<?php

/**
 * Contact Form Component
 * 
 * @param string $example Example prop
 * 
 */

// Build CSS classes
$prefix = 'contact-form';
$id = $props['id'] ?? 'e350cea';
$shortcode = '[contact-form-7 id="' . $id . '" title="Produktförfrågan"]';
// Get contact-form.css styling
wp_enqueue_style('contact_form-style', get_stylesheet_directory_uri() . '/components/contact-form/contact-form.css', array(), '1.0', 'all');
// Render the component
?>

<div class="<?= $prefix; ?>" id="inquiry-form-wrapper">
  <?php echo do_shortcode($shortcode); ?>
</div>